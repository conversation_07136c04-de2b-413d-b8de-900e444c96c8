import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/typography.dart';
import '../../../../core/animations/animation_utils.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../domain/models/today_workout.dart';
import '../../domain/providers/dashboard_provider.dart';

class TodayWorkoutCard extends ConsumerStatefulWidget {
  const TodayWorkoutCard({super.key});

  @override
  ConsumerState<TodayWorkoutCard> createState() => _TodayWorkoutCardState();
}

class _TodayWorkoutCardState extends ConsumerState<TodayWorkoutCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: FitnessAnimationCurves.springCurve,
      ),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOut,
      ),
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final todayWorkoutAsync = ref.watch(todayWorkoutProvider);

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 20),
              child: todayWorkoutAsync.when(
                data: (workout) => workout != null
                    ? _buildWorkoutCard(context, theme, workout)
                    : _buildNoWorkoutCard(context, theme),
                loading: () => _buildLoadingCard(context, theme),
                error: (error, stack) => _buildErrorCard(context, theme),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildWorkoutCard(BuildContext context, ThemeData theme, TodayWorkout workout) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: AppColorPalette.primaryOrange.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: Stack(
          children: [
            // Background image with blur
            _buildBackgroundImage(workout),
            
            // Glass morphism overlay
            BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 2, sigmaY: 2),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.black.withOpacity(0.3),
                      Colors.black.withOpacity(0.6),
                    ],
                  ),
                ),
              ),
            ),
            
            // Content
            Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Today\'s Workout',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: Colors.white.withOpacity(0.9),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      _buildStatusBadge(workout),
                    ],
                  ),
                  
                  const Spacer(),
                  
                  // Workout name
                  Text(
                    workout.name,
                    style: AppTypography.displayNumbers(
                      fontSize: 24,
                      color: Colors.white,
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // Quick stats
                  Text(
                    workout.quickStatsText,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: Colors.white.withOpacity(0.9),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Action button
                  _buildActionButton(context, workout),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackgroundImage(TodayWorkout workout) {
    if (workout.backgroundImageUrl != null) {
      return Positioned.fill(
        child: Image.network(
          workout.backgroundImageUrl!,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => _buildDefaultBackground(),
        ),
      );
    }
    return _buildDefaultBackground();
  }

  Widget _buildDefaultBackground() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColorPalette.primaryOrange,
              AppColorPalette.primaryOrangeLight,
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(TodayWorkout workout) {
    Color badgeColor;
    String statusText;
    
    if (workout.isCompleted) {
      badgeColor = AppColorPalette.successGreen;
      statusText = 'Completed';
    } else if (workout.isStarted) {
      badgeColor = AppColorPalette.warning;
      statusText = 'In Progress';
    } else {
      badgeColor = Colors.white.withOpacity(0.2);
      statusText = 'Ready';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: badgeColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildActionButton(BuildContext context, TodayWorkout workout) {
    String buttonText;
    VoidCallback? onPressed;
    
    if (workout.isCompleted) {
      buttonText = 'View Summary';
      onPressed = () => _handleViewSummary(context, workout);
    } else if (workout.canResume) {
      buttonText = 'Resume Workout';
      onPressed = () => _handleResumeWorkout(context, workout);
    } else {
      buttonText = 'Start Workout';
      onPressed = () => _handleStartWorkout(context, workout);
    }

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColorPalette.primaryOrange,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
        ),
        child: Text(
          buttonText,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildNoWorkoutCard(BuildContext context, ThemeData theme) {
    return GlassMorphismCard(
      height: 200,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.fitness_center_outlined,
              size: 48,
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No workout assigned',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Check back tomorrow for your next workout',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingCard(BuildContext context, ThemeData theme) {
    return GlassMorphismCard(
      height: 200,
      child: Center(
        child: CircularProgressIndicator(
          color: AppColorPalette.primaryOrange,
        ),
      ),
    );
  }

  Widget _buildErrorCard(BuildContext context, ThemeData theme) {
    return GlassMorphismCard(
      height: 200,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: AppColorPalette.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Unable to load workout',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            TextButton(
              onPressed: () => ref.refresh(todayWorkoutProvider),
              child: Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  void _handleStartWorkout(BuildContext context, TodayWorkout workout) {
    HapticFeedback.mediumImpact();
    // TODO: Navigate to pre-workout screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Starting ${workout.name}...')),
    );
  }

  void _handleResumeWorkout(BuildContext context, TodayWorkout workout) {
    HapticFeedback.mediumImpact();
    // TODO: Navigate to active workout screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Resuming ${workout.name}...')),
    );
  }

  void _handleViewSummary(BuildContext context, TodayWorkout workout) {
    HapticFeedback.lightImpact();
    // TODO: Navigate to workout summary screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Viewing summary for ${workout.name}')),
    );
  }
}
