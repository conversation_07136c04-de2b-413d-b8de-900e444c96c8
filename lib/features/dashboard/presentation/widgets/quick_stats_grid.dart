import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/typography.dart';
import '../../../../core/animations/animation_utils.dart';
import '../../../../core/animations/counting_animation.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../domain/models/user_stats.dart';
import '../../domain/providers/dashboard_provider.dart';

class QuickStatsGrid extends ConsumerStatefulWidget {
  const QuickStatsGrid({super.key});

  @override
  ConsumerState<QuickStatsGrid> createState() => _QuickStatsGridState();
}

class _QuickStatsGridState extends ConsumerState<QuickStatsGrid>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<Offset>> _slideAnimations;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startStaggeredAnimations();
  }

  void _setupAnimations() {
    _controllers = List.generate(
      3,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 600),
        vsync: this,
      ),
    );

    _scaleAnimations = _controllers.map((controller) {
      return Tween<double>(
        begin: 0.8,
        end: 1.0,
      ).animate(
        CurvedAnimation(
          parent: controller,
          curve: FitnessAnimationCurves.springCurve,
        ),
      );
    }).toList();

    _slideAnimations = _controllers.map((controller) {
      return Tween<Offset>(
        begin: const Offset(0, 0.5),
        end: Offset.zero,
      ).animate(
        CurvedAnimation(
          parent: controller,
          curve: Curves.easeOutCubic,
        ),
      );
    }).toList();
  }

  void _startStaggeredAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 100), () {
        if (mounted) {
          _controllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final userStatsAsync = ref.watch(userStatsProvider);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Stats',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
              color: theme.colorScheme.onBackground,
            ),
          ),
          const SizedBox(height: 16),
          userStatsAsync.when(
            data: (stats) => _buildStatsGrid(context, theme, stats),
            loading: () => _buildLoadingGrid(context, theme),
            error: (error, stack) => _buildErrorGrid(context, theme),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsGrid(BuildContext context, ThemeData theme, UserStats stats) {
    final statItems = [
      _StatItem(
        title: 'Current Streak',
        value: stats.currentStreak.toDouble(),
        unit: stats.currentStreak == 1 ? 'day' : 'days',
        icon: Icons.local_fire_department,
        color: AppColorPalette.primaryOrange,
        subtitle: stats.streakText,
      ),
      _StatItem(
        title: 'Weekly Progress',
        value: stats.weeklyWorkouts.toDouble(),
        unit: stats.weeklyWorkouts == 1 ? 'workout' : 'workouts',
        icon: Icons.trending_up,
        color: AppColorPalette.successGreen,
        subtitle: 'This week',
      ),
      _StatItem(
        title: 'Calories Burned',
        value: stats.weeklyCalories.toDouble(),
        unit: 'kcal',
        icon: Icons.whatshot,
        color: AppColorPalette.accentBlue,
        subtitle: 'This week',
      ),
    ];

    return Row(
      children: statItems.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;
        
        return Expanded(
          child: AnimatedBuilder(
            animation: _controllers[index],
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimations[index].value,
                child: SlideTransition(
                  position: _slideAnimations[index],
                  child: Container(
                    margin: EdgeInsets.only(
                      right: index < statItems.length - 1 ? 12 : 0,
                    ),
                    child: _buildStatCard(context, theme, item, index),
                  ),
                ),
              );
            },
          ),
        );
      }).toList(),
    );
  }

  Widget _buildStatCard(BuildContext context, ThemeData theme, _StatItem item, int index) {
    return GlassMorphismCard(
      onTap: () => _handleStatTap(index, item),
      enableHapticFeedback: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon container
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: item.color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              item.icon,
              color: item.color,
              size: 20,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Value with counting animation
          CountingAnimation(
            value: item.value,
            duration: Duration(milliseconds: 800 + (index * 200)),
            textStyle: AppTypography.displayNumbers(
              fontSize: 24,
              color: theme.colorScheme.onSurface,
            ),
          ),
          
          // Unit
          Text(
            item.unit,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Title
          Text(
            item.title,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          
          // Subtitle
          if (item.subtitle != null)
            Text(
              item.subtitle!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.6),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLoadingGrid(BuildContext context, ThemeData theme) {
    return Row(
      children: List.generate(3, (index) {
        return Expanded(
          child: Container(
            margin: EdgeInsets.only(right: index < 2 ? 12 : 0),
            child: GlassMorphismCard(
              height: 120,
              child: Center(
                child: CircularProgressIndicator(
                  color: AppColorPalette.primaryOrange,
                  strokeWidth: 2,
                ),
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildErrorGrid(BuildContext context, ThemeData theme) {
    return GlassMorphismCard(
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.error_outline,
              color: AppColorPalette.error,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              'Unable to load stats',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            TextButton(
              onPressed: () => ref.refresh(userStatsProvider),
              child: Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  void _handleStatTap(int index, _StatItem item) {
    HapticFeedback.lightImpact();
    
    // Add subtle animation feedback
    _controllers[index].reverse().then((_) {
      _controllers[index].forward();
    });

    // TODO: Navigate to detailed stats view
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Viewing ${item.title.toLowerCase()} details'),
        duration: const Duration(seconds: 1),
      ),
    );
  }
}

class _StatItem {
  final String title;
  final double value;
  final String unit;
  final IconData icon;
  final Color color;
  final String? subtitle;

  const _StatItem({
    required this.title,
    required this.value,
    required this.unit,
    required this.icon,
    required this.color,
    this.subtitle,
  });
}
