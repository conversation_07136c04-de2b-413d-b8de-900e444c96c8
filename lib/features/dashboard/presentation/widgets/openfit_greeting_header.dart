import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/spacing.dart';
import '../../../profile/domain/providers/profile_provider.dart';

/// OpenFit greeting header with dynamic time-based greeting and profile avatar
class OpenFitGreetingHeader extends ConsumerWidget {
  const OpenFitGreetingHeader({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final profileState = ref.watch(profileProvider);

    return Padding(
      padding: const EdgeInsets.all(AppSpacing.lg),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Greeting section
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getTimeBasedGreeting(),
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                    fontSize: 24,
                  ),
                ),
                const SizedBox(height: AppSpacing.xs),
                profileState.when(
                  data: (profile) => Text(
                    _getMotivationalSubtitle(profile?.displayName),
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 16,
                    ),
                  ),
                  loading: () => Text(
                    'Ready to crush your goals?',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 16,
                    ),
                  ),
                  error: (error, stack) => Text(
                    'Ready to crush your goals?',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Profile avatar with notification badge
          _buildProfileAvatar(context, ref),
        ],
      ),
    );
  }

  Widget _buildProfileAvatar(BuildContext context, WidgetRef ref) {
    final profileState = ref.watch(profileProvider);
    
    return Stack(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: AppColorPalette.primaryGradient,
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColorPalette.primaryOrange.withOpacity(0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(24),
              onTap: () {
                // TODO: Navigate to profile screen
                // Navigator.of(context).pushNamed('/profile');
              },
              child: profileState.when(
                data: (profile) => _buildAvatarContent(profile?.displayName),
                loading: () => _buildAvatarContent(null),
                error: (error, stack) => _buildAvatarContent(null),
              ),
            ),
          ),
        ),
        
        // Notification badge (optional)
        Positioned(
          top: 0,
          right: 0,
          child: Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: AppColorPalette.error,
              shape: BoxShape.circle,
              border: Border.all(
                color: AppColorPalette.darkBackground,
                width: 2,
              ),
            ),
            child: Center(
              child: Text(
                '2',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAvatarContent(String? displayName) {
    if (displayName != null && displayName.isNotEmpty) {
      return Center(
        child: Text(
          displayName[0].toUpperCase(),
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }
    
    return Icon(
      Icons.person,
      color: Colors.white,
      size: 24,
    );
  }

  String _getTimeBasedGreeting() {
    final hour = DateTime.now().hour;
    
    if (hour < 12) {
      return 'Good morning! ☀️';
    } else if (hour < 17) {
      return 'Good afternoon! 🌤️';
    } else {
      return 'Good evening! 🌙';
    }
  }

  String _getMotivationalSubtitle(String? displayName) {
    final hour = DateTime.now().hour;
    final name = displayName ?? 'Champion';
    
    final morningMessages = [
      'Ready to start strong, $name?',
      'Time to energize your day!',
      'Let\'s make today count!',
    ];
    
    final afternoonMessages = [
      'Keep the momentum going!',
      'Power through, $name!',
      'You\'ve got this!',
    ];
    
    final eveningMessages = [
      'Finish strong, $name!',
      'End the day on a high note!',
      'One more push to greatness!',
    ];
    
    List<String> messages;
    if (hour < 12) {
      messages = morningMessages;
    } else if (hour < 17) {
      messages = afternoonMessages;
    } else {
      messages = eveningMessages;
    }
    
    // Use day of year to get consistent but varying message
    final dayOfYear = DateTime.now().difference(DateTime(DateTime.now().year)).inDays;
    return messages[dayOfYear % messages.length];
  }
}

/// Animated greeting header with typewriter effect
class AnimatedGreetingHeader extends StatefulWidget {
  final String greeting;
  final String subtitle;

  const AnimatedGreetingHeader({
    super.key,
    required this.greeting,
    required this.subtitle,
  });

  @override
  State<AnimatedGreetingHeader> createState() => _AnimatedGreetingHeaderState();
}

class _AnimatedGreetingHeaderState extends State<AnimatedGreetingHeader>
    with TickerProviderStateMixin {
  late AnimationController _typewriterController;
  late Animation<int> _greetingAnimation;
  late Animation<int> _subtitleAnimation;

  @override
  void initState() {
    super.initState();
    
    _typewriterController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _greetingAnimation = IntTween(
      begin: 0,
      end: widget.greeting.length,
    ).animate(CurvedAnimation(
      parent: _typewriterController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _subtitleAnimation = IntTween(
      begin: 0,
      end: widget.subtitle.length,
    ).animate(CurvedAnimation(
      parent: _typewriterController,
      curve: const Interval(0.6, 1.0, curve: Curves.easeOut),
    ));

    _typewriterController.forward();
  }

  @override
  void dispose() {
    _typewriterController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AnimatedBuilder(
      animation: _typewriterController,
      builder: (context, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.greeting.substring(0, _greetingAnimation.value),
              style: theme.textTheme.headlineSmall?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w700,
                fontSize: 24,
              ),
            ),
            const SizedBox(height: AppSpacing.xs),
            Text(
              widget.subtitle.substring(0, _subtitleAnimation.value),
              style: theme.textTheme.bodyLarge?.copyWith(
                color: Colors.white.withOpacity(0.8),
                fontSize: 16,
              ),
            ),
          ],
        );
      },
    );
  }
}
