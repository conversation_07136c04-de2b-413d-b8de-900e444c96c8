class TodayWorkout {
  final String id;
  final String name;
  final String description;
  final int estimatedDuration; // in minutes
  final int estimatedCalories;
  final int totalSets;
  final List<TodayWorkoutExercise> exercises;
  final String? backgroundImageUrl;
  final bool isCompleted;
  final bool isStarted;
  final DateTime? startedAt;
  final DateTime? completedAt;

  const TodayWorkout({
    required this.id,
    required this.name,
    required this.description,
    required this.estimatedDuration,
    required this.estimatedCalories,
    required this.totalSets,
    required this.exercises,
    this.backgroundImageUrl,
    this.isCompleted = false,
    this.isStarted = false,
    this.startedAt,
    this.completedAt,
  });

  factory TodayWorkout.fromJson(Map<String, dynamic> json) {
    return TodayWorkout(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      estimatedDuration: json['estimatedDuration'] ?? 0,
      estimatedCalories: json['estimatedCalories'] ?? 0,
      totalSets: json['totalSets'] ?? 0,
      exercises: (json['exercises'] as List<dynamic>?)
          ?.map((e) => TodayWorkoutExercise.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      backgroundImageUrl: json['backgroundImageUrl'],
      isCompleted: json['isCompleted'] ?? false,
      isStarted: json['isStarted'] ?? false,
      startedAt: json['startedAt'] != null ? DateTime.parse(json['startedAt']) : null,
      completedAt: json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'estimatedDuration': estimatedDuration,
      'estimatedCalories': estimatedCalories,
      'totalSets': totalSets,
      'exercises': exercises.map((e) => e.toJson()).toList(),
      'backgroundImageUrl': backgroundImageUrl,
      'isCompleted': isCompleted,
      'isStarted': isStarted,
      'startedAt': startedAt?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
    };
  }
}

class TodayWorkoutExercise {
  final String id;
  final String name;
  final String description;
  final int sets;
  final List<int> reps;
  final List<double> weights; // in lbs (imperial)
  final String? videoUrl;
  final String? thumbnailUrl;
  final String? instructions;
  final int restInterval; // in seconds
  final bool isCompleted;

  const TodayWorkoutExercise({
    required this.id,
    required this.name,
    required this.description,
    required this.sets,
    required this.reps,
    required this.weights,
    this.videoUrl,
    this.thumbnailUrl,
    this.instructions,
    this.restInterval = 60,
    this.isCompleted = false,
  });

  factory TodayWorkoutExercise.fromJson(Map<String, dynamic> json) {
    return TodayWorkoutExercise(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      sets: json['sets'] ?? 3,
      reps: (json['reps'] as List<dynamic>?)?.cast<int>() ?? [10],
      weights: (json['weights'] as List<dynamic>?)?.cast<double>() ?? [0.0],
      videoUrl: json['videoUrl'],
      thumbnailUrl: json['thumbnailUrl'],
      instructions: json['instructions'],
      restInterval: json['restInterval'] ?? 60,
      isCompleted: json['isCompleted'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'sets': sets,
      'reps': reps,
      'weights': weights,
      'videoUrl': videoUrl,
      'thumbnailUrl': thumbnailUrl,
      'instructions': instructions,
      'restInterval': restInterval,
      'isCompleted': isCompleted,
    };
  }
}

/// Extension for TodayWorkout to provide formatted values and utilities
extension TodayWorkoutExtension on TodayWorkout {
  /// Get duration text in imperial format
  String get durationText => '$estimatedDuration min';

  /// Get calories text in imperial format
  String get caloriesText => '$estimatedCalories kcal';

  /// Get sets text with proper pluralization
  String get setsText {
    if (totalSets == 1) return '1 set';
    return '$totalSets sets';
  }

  /// Get quick stats text for display
  String get quickStatsText => '$durationText • $caloriesText • $setsText';

  /// Get completion percentage
  double get completionPercentage {
    if (exercises.isEmpty) return 0.0;
    final completedExercises = exercises.where((e) => e.isCompleted).length;
    return completedExercises / exercises.length;
  }

  /// Get next exercise to perform
  TodayWorkoutExercise? get nextExercise {
    try {
      return exercises.firstWhere(
        (exercise) => !exercise.isCompleted,
      );
    } catch (e) {
      return exercises.isNotEmpty ? exercises.first : null;
    }
  }

  /// Check if workout can be started
  bool get canStart => !isCompleted && !isStarted;

  /// Check if workout can be resumed
  bool get canResume => isStarted && !isCompleted;

  /// Get status text for display
  String get statusText {
    if (isCompleted) return 'Completed';
    if (isStarted) return 'In Progress';
    return 'Ready to Start';
  }

  /// Get motivational start message
  String get startMessage {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Start your day strong!';
    } else if (hour < 17) {
      return 'Power through your workout!';
    } else {
      return 'Finish strong today!';
    }
  }
}
