import 'user_stats.dart';
import 'today_workout.dart';

class DashboardData {
  final UserStats userStats;
  final TodayWorkout? todayWorkout;

  const DashboardData({
    required this.userStats,
    this.todayWorkout,
  });

  factory DashboardData.fromJson(Map<String, dynamic> json) {
    return DashboardData(
      userStats: UserStats.fromJson(json['userStats'] as Map<String, dynamic>),
      todayWorkout: json['todayWorkout'] != null
          ? TodayWorkout.fromJson(json['todayWorkout'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userStats': userStats.toJson(),
      'todayWorkout': todayWorkout?.toJson(),
    };
  }
}
