class UserStats {
  final int currentStreak;
  final int weeklyWorkouts;
  final int weeklyCalories;
  final int totalWorkouts;
  final double averageWorkoutDuration; // in minutes
  final List<WeeklyActivity> weeklyActivity;
  final DateTime lastWorkoutDate;
  final bool hasWorkoutToday;

  // Additional properties for OpenFit quick stats
  final int workoutsThisWeek;
  final int totalCalories;

  const UserStats({
    required this.currentStreak,
    required this.weeklyWorkouts,
    required this.weeklyCalories,
    required this.totalWorkouts,
    required this.averageWorkoutDuration,
    required this.weeklyActivity,
    required this.lastWorkoutDate,
    this.hasWorkoutToday = false,
    required this.workoutsThisWeek,
    required this.totalCalories,
  });

  factory UserStats.fromJson(Map<String, dynamic> json) {
    return UserStats(
      currentStreak: json['currentStreak'] ?? 0,
      weeklyWorkouts: json['weeklyWorkouts'] ?? 0,
      weeklyCalories: json['weeklyCalories'] ?? 0,
      totalWorkouts: json['totalWorkouts'] ?? 0,
      averageWorkoutDuration: (json['averageWorkoutDuration'] ?? 0.0).toDouble(),
      weeklyActivity: (json['weeklyActivity'] as List<dynamic>?)
          ?.map((e) => WeeklyActivity.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      lastWorkoutDate: DateTime.parse(json['lastWorkoutDate'] ?? DateTime.now().toIso8601String()),
      hasWorkoutToday: json['hasWorkoutToday'] ?? false,
      workoutsThisWeek: json['workoutsThisWeek'] ?? json['weeklyWorkouts'] ?? 0,
      totalCalories: json['totalCalories'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'currentStreak': currentStreak,
      'weeklyWorkouts': weeklyWorkouts,
      'weeklyCalories': weeklyCalories,
      'totalWorkouts': totalWorkouts,
      'averageWorkoutDuration': averageWorkoutDuration,
      'weeklyActivity': weeklyActivity.map((e) => e.toJson()).toList(),
      'lastWorkoutDate': lastWorkoutDate.toIso8601String(),
      'hasWorkoutToday': hasWorkoutToday,
      'workoutsThisWeek': workoutsThisWeek,
      'totalCalories': totalCalories,
    };
  }
}

class WeeklyActivity {
  final String day;
  final int minutes;
  final int calories;
  final DateTime date;

  const WeeklyActivity({
    required this.day,
    required this.minutes,
    required this.calories,
    required this.date,
  });

  factory WeeklyActivity.fromJson(Map<String, dynamic> json) {
    return WeeklyActivity(
      day: json['day'] ?? '',
      minutes: json['minutes'] ?? 0,
      calories: json['calories'] ?? 0,
      date: DateTime.parse(json['date'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'day': day,
      'minutes': minutes,
      'calories': calories,
      'date': date.toIso8601String(),
    };
  }
}

/// Extension for UserStats to provide formatted values
extension UserStatsExtension on UserStats {
  /// Get streak text with proper pluralization
  String get streakText {
    if (currentStreak == 0) return 'Start your streak!';
    if (currentStreak == 1) return '1 day streak!';
    return '$currentStreak day streak!';
  }

  /// Get weekly calories in imperial format (kcal)
  String get weeklyCaloriesText => '${weeklyCalories.toStringAsFixed(0)} kcal';

  /// Get average workout duration in minutes
  String get averageDurationText => '${averageWorkoutDuration.toInt()} min avg';

  /// Check if user worked out yesterday (for streak calculation)
  bool get workedOutYesterday {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return lastWorkoutDate.year == yesterday.year &&
           lastWorkoutDate.month == yesterday.month &&
           lastWorkoutDate.day == yesterday.day;
  }

  /// Get motivational message based on stats
  String get motivationalMessage {
    if (currentStreak >= 7) {
      return 'Amazing! You\'re on fire! 🔥';
    } else if (currentStreak >= 3) {
      return 'Great momentum! Keep it up! 💪';
    } else if (weeklyWorkouts >= 3) {
      return 'Solid week of training! 🎯';
    } else {
      return 'Every workout counts! 🌟';
    }
  }
}
