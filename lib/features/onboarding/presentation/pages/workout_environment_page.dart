import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/providers/onboarding_provider.dart';
import '../../../profile/domain/models/user_profile.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';

class WorkoutEnvironmentPage extends ConsumerWidget {
  const WorkoutEnvironmentPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(onboardingProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        children: [
          // Environment Selection
          ...WorkoutEnvironment.values.map((environment) {
            final isSelected = state.workoutEnvironments.contains(environment.displayName);
            
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: GestureDetector(
                onTap: () {
                  ref.read(onboardingProvider.notifier).toggleWorkoutEnvironment(environment.displayName);
                },
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.05),
                    border: Border.all(
                      color: isSelected
                          ? const Color(0xFFFF6B35)
                          : Colors.white.withOpacity(0.2),
                      width: isSelected ? 2 : 1,
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Row(
                      children: [
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: isSelected
                                ? const Color(0xFFFF6B35).withOpacity(0.2)
                                : Colors.white.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            _getEnvironmentIcon(environment),
                            color: isSelected ? const Color(0xFFFF6B35) : Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                environment.displayName,
                                style: TextStyle(
                                  color: isSelected ? const Color(0xFFFF6B35) : Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                _getEnvironmentDescription(environment),
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.7),
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (isSelected)
                          Container(
                            width: 24,
                            height: 24,
                            decoration: const BoxDecoration(
                              color: Color(0xFFFF6B35),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }).toList(),

          const SizedBox(height: 24),

          // Equipment Selection (if any environment is selected)
          if (state.workoutEnvironments.isNotEmpty) ...[
            GlassMorphismCard(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Available Equipment',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Select the equipment you have access to',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.7),
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Equipment grid
                    GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: 2,
                      mainAxisSpacing: 12,
                      crossAxisSpacing: 12,
                      childAspectRatio: 3,
                      children: _getAvailableEquipment(state.workoutEnvironments)
                          .map((equipment) {
                        final isSelected = state.availableEquipment.contains(equipment);
                        
                        return GestureDetector(
                          onTap: () {
                            ref.read(onboardingProvider.notifier).toggleEquipment(equipment);
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? const Color(0xFFFF6B35).withOpacity(0.2)
                                  : Colors.white.withOpacity(0.1),
                              border: Border.all(
                                color: isSelected
                                    ? const Color(0xFFFF6B35)
                                    : Colors.white.withOpacity(0.3),
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Center(
                              child: Text(
                                equipment,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: isSelected ? const Color(0xFFFF6B35) : Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ),
            ),
          ],

          const SizedBox(height: 40),
        ],
      ),
    );
  }

  IconData _getEnvironmentIcon(WorkoutEnvironment environment) {
    switch (environment) {
      case WorkoutEnvironment.largeGym:
        return Icons.fitness_center;
      case WorkoutEnvironment.smallGym:
        return Icons.sports_gymnastics;
      case WorkoutEnvironment.homeWithEquipment:
        return Icons.home;
      case WorkoutEnvironment.homeWithoutEquipment:
        return Icons.self_improvement;
    }
  }

  String _getEnvironmentDescription(WorkoutEnvironment environment) {
    switch (environment) {
      case WorkoutEnvironment.largeGym:
        return 'Full commercial gym with all equipment';
      case WorkoutEnvironment.smallGym:
        return 'Basic gym with essential equipment';
      case WorkoutEnvironment.homeWithEquipment:
        return 'Home gym with weights and machines';
      case WorkoutEnvironment.homeWithoutEquipment:
        return 'Bodyweight exercises at home';
    }
  }

  List<String> _getAvailableEquipment(List<String> environments) {
    Set<String> equipment = {};
    
    for (String env in environments) {
      switch (env) {
        case 'Large Gym (full equipment)':
          equipment.addAll([
            'Barbells', 'Dumbbells', 'Cable Machine', 'Leg Press',
            'Bench Press', 'Squat Rack', 'Pull-up Bar', 'Treadmill',
            'Rowing Machine', 'Elliptical', 'Kettlebells', 'Resistance Bands',
          ]);
          break;
        case 'Small Gym (basic equipment)':
          equipment.addAll([
            'Dumbbells', 'Bench', 'Pull-up Bar', 'Kettlebells',
            'Resistance Bands', 'Medicine Ball', 'Yoga Mat',
          ]);
          break;
        case 'Home with Equipment':
          equipment.addAll([
            'Dumbbells', 'Resistance Bands', 'Yoga Mat', 'Pull-up Bar',
            'Kettlebells', 'Medicine Ball', 'Bench',
          ]);
          break;
        case 'Home without Equipment':
          equipment.addAll([
            'Yoga Mat', 'Resistance Bands', 'Water Bottles',
          ]);
          break;
      }
    }
    
    return equipment.toList()..sort();
  }
}
