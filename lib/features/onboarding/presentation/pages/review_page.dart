import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/providers/onboarding_provider.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';

class ReviewPage extends ConsumerWidget {
  const ReviewPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(onboardingProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        children: [
          // Personal Information Summary
          _SummaryCard(
            title: 'Personal Information',
            icon: Icons.person,
            onEdit: () => ref.read(onboardingProvider.notifier).goToPage(0),
            children: [
              if (state.name != null) _SummaryItem('Name', state.name!),
              if (state.gender != null) _SummaryItem('Gender', state.gender!),
              if (state.age != null) _SummaryItem('Age', '${state.age} years old'),
              if (state.height != null) 
                _SummaryItem('Height', '${state.height} ${state.heightUnit}'),
              if (state.weight != null) 
                _SummaryItem('Weight', '${state.weight} ${state.weightUnit}'),
            ],
          ),

          const SizedBox(height: 16),

          // Fitness Goals Summary
          _SummaryCard(
            title: 'Fitness Goals',
            icon: Icons.flag,
            onEdit: () => ref.read(onboardingProvider.notifier).goToPage(1),
            children: [
              if (state.selectedGoals.isNotEmpty)
                _SummaryItem('Primary Goal', state.selectedGoals.first),
              if (state.selectedGoals.length > 1)
                _SummaryItem('Additional Goals', 
                  state.selectedGoals.skip(1).join(', ')),
              if (state.sportActivity != null && state.sportActivity!.isNotEmpty)
                _SummaryItem('Sport/Activity', state.sportActivity!),
            ],
          ),

          const SizedBox(height: 16),

          // Fitness Levels Summary
          _SummaryCard(
            title: 'Fitness Levels',
            icon: Icons.trending_up,
            onEdit: () => ref.read(onboardingProvider.notifier).goToPage(2),
            children: [
              if (state.cardioLevel != null)
                _SummaryItem('Cardio Level', 'Level ${state.cardioLevel}'),
              if (state.weightliftingLevel != null)
                _SummaryItem('Weightlifting Level', 'Level ${state.weightliftingLevel}'),
              if (state.exercisesToAvoid.isNotEmpty)
                _SummaryItem('Exercises to Avoid', state.exercisesToAvoid.join(', ')),
            ],
          ),

          const SizedBox(height: 16),

          // Workout Environment Summary
          _SummaryCard(
            title: 'Workout Environment',
            icon: Icons.fitness_center,
            onEdit: () => ref.read(onboardingProvider.notifier).goToPage(3),
            children: [
              if (state.workoutEnvironments.isNotEmpty)
                _SummaryItem('Environment', state.workoutEnvironments.join(', ')),
              if (state.availableEquipment.isNotEmpty)
                _SummaryItem('Equipment', state.availableEquipment.take(3).join(', ') +
                  (state.availableEquipment.length > 3 ? '...' : '')),
            ],
          ),

          const SizedBox(height: 16),

          // Workout Schedule Summary
          _SummaryCard(
            title: 'Workout Schedule',
            icon: Icons.schedule,
            onEdit: () => ref.read(onboardingProvider.notifier).goToPage(4),
            children: [
              if (state.workoutFrequency != null)
                _SummaryItem('Frequency', '${state.workoutFrequency} days per week'),
              if (state.sessionDuration != null)
                _SummaryItem('Duration', state.sessionDuration!),
              if (state.preferredDays.isNotEmpty)
                _SummaryItem('Preferred Days', state.preferredDays.join(', ')),
            ],
          ),

          const SizedBox(height: 16),

          // Additional Information Summary
          if (state.additionalNotes != null && state.additionalNotes!.isNotEmpty ||
              state.healthConditions.isNotEmpty ||
              state.dietaryRestrictions.isNotEmpty)
            _SummaryCard(
              title: 'Additional Information',
              icon: Icons.info,
              onEdit: () => ref.read(onboardingProvider.notifier).goToPage(5),
              children: [
                if (state.additionalNotes != null && state.additionalNotes!.isNotEmpty)
                  _SummaryItem('Notes', state.additionalNotes!),
                if (state.healthConditions.isNotEmpty)
                  _SummaryItem('Health Considerations', state.healthConditions.join(', ')),
                if (state.dietaryRestrictions.isNotEmpty)
                  _SummaryItem('Dietary Preferences', state.dietaryRestrictions.join(', ')),
              ],
            ),

          const SizedBox(height: 24),

          // Completion Message
          GlassMorphismCard(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                children: [
                  Container(
                    width: 64,
                    height: 64,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFFFF6B35), Color(0xFFFF8E53)],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(32),
                    ),
                    child: const Icon(
                      Icons.rocket_launch,
                      color: Colors.white,
                      size: 32,
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Ready to Start Your Journey!',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'We\'ll use this information to create your personalized fitness plan. You can always update your preferences later.',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 16,
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 40),
        ],
      ),
    );
  }
}

class _SummaryCard extends StatelessWidget {
  final String title;
  final IconData icon;
  final VoidCallback onEdit;
  final List<Widget> children;

  const _SummaryCard({
    required this.title,
    required this.icon,
    required this.onEdit,
    required this.children,
  });

  @override
  Widget build(BuildContext context) {
    return GlassMorphismCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: const Color(0xFFFF6B35).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: const Color(0xFFFF6B35),
                    size: 18,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                TextButton(
                  onPressed: onEdit,
                  child: const Text(
                    'Edit',
                    style: TextStyle(
                      color: Color(0xFFFF6B35),
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            if (children.isNotEmpty) ...[
              const SizedBox(height: 16),
              ...children,
            ],
          ],
        ),
      ),
    );
  }
}

class _SummaryItem extends StatelessWidget {
  final String label;
  final String value;

  const _SummaryItem(this.label, this.value);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
