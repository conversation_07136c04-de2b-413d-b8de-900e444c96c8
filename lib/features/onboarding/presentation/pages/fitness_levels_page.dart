import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/providers/onboarding_provider.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';

class FitnessLevelsPage extends ConsumerWidget {
  const FitnessLevelsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(onboardingProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        children: [
          // Cardio Level
          GlassMorphismCard(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Cardio Endurance',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _getCardioDescription(state.cardioLevel ?? 1),
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 20),
                  _FitnessSlider(
                    value: (state.cardioLevel ?? 1).toDouble(),
                    min: 1,
                    max: 6,
                    divisions: 5,
                    onChanged: (value) {
                      ref.read(onboardingProvider.notifier).updateFitnessLevels(
                        cardioLevel: value.round(),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Weightlifting Level
          GlassMorphismCard(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Weightlifting Experience',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _getWeightliftingDescription(state.weightliftingLevel ?? 1),
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 20),
                  _FitnessSlider(
                    value: (state.weightliftingLevel ?? 1).toDouble(),
                    min: 1,
                    max: 5,
                    divisions: 4,
                    onChanged: (value) {
                      ref.read(onboardingProvider.notifier).updateFitnessLevels(
                        weightliftingLevel: value.round(),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Exercises to Avoid
          GlassMorphismCard(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Exercises to Avoid (Optional)',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Select any exercises you want to avoid due to injuries or preferences',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: [
                      'Squats',
                      'Deadlifts',
                      'Bench Press',
                      'Pull-ups',
                      'Running',
                      'Jumping',
                      'Overhead Press',
                      'Lunges',
                    ].map((exercise) {
                      final isSelected = state.exercisesToAvoid.contains(exercise);
                      return GestureDetector(
                        onTap: () {
                          ref.read(onboardingProvider.notifier).toggleExerciseToAvoid(exercise);
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? const Color(0xFFFF6B35).withOpacity(0.2)
                                : Colors.white.withOpacity(0.1),
                            border: Border.all(
                              color: isSelected
                                  ? const Color(0xFFFF6B35)
                                  : Colors.white.withOpacity(0.3),
                            ),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            exercise,
                            style: TextStyle(
                              color: isSelected ? const Color(0xFFFF6B35) : Colors.white,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 40),
        ],
      ),
    );
  }

  String _getCardioDescription(int level) {
    switch (level) {
      case 1:
        return 'I cannot walk half a mile without getting winded';
      case 2:
        return 'I can walk a mile but struggle with stairs';
      case 3:
        return 'I can jog for 10-15 minutes continuously';
      case 4:
        return 'I can run 2-3 miles comfortably';
      case 5:
        return 'I can run 4-6 miles regularly';
      case 6:
        return 'I can run 6+ miles and do endurance sports';
      default:
        return '';
    }
  }

  String _getWeightliftingDescription(int level) {
    switch (level) {
      case 1:
        return 'Complete beginner - never lifted weights';
      case 2:
        return 'Novice - some experience with basic exercises';
      case 3:
        return 'Intermediate - comfortable with most exercises';
      case 4:
        return 'Advanced - years of consistent training';
      case 5:
        return 'Expert - competition level or training others';
      default:
        return '';
    }
  }
}

class _FitnessSlider extends StatelessWidget {
  final double value;
  final double min;
  final double max;
  final int divisions;
  final ValueChanged<double> onChanged;

  const _FitnessSlider({
    required this.value,
    required this.min,
    required this.max,
    required this.divisions,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: const Color(0xFFFF6B35),
            inactiveTrackColor: Colors.white.withOpacity(0.2),
            thumbColor: const Color(0xFFFF6B35),
            overlayColor: const Color(0xFFFF6B35).withOpacity(0.2),
            valueIndicatorColor: const Color(0xFFFF6B35),
            valueIndicatorTextStyle: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          child: Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            label: value.round().toString(),
            onChanged: onChanged,
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Level ${min.round()}',
              style: TextStyle(
                color: Colors.white.withOpacity(0.5),
                fontSize: 12,
              ),
            ),
            Text(
              'Level ${max.round()}',
              style: TextStyle(
                color: Colors.white.withOpacity(0.5),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
