import '../../../../shared/services/supabase_service.dart';
import '../../../profile/domain/models/user_profile.dart';

/// Repository for onboarding and profile data
class OnboardingRepository {
  /// Save user profile to Supabase
  Future<void> saveUserProfile(UserProfile profile) async {
    try {
      final data = _profileToSupabaseMap(profile);
      
      await SupabaseService.client
          .from('profiles')
          .upsert(data);
    } catch (e) {
      throw Exception('Failed to save profile: $e');
    }
  }

  /// Get user profile from Supabase
  Future<UserProfile?> getUserProfile(String userId) async {
    try {
      final response = await SupabaseService.client
          .from('profiles')
          .select()
          .eq('id', userId)
          .maybeSingle();

      if (response == null) return null;

      return _supabaseMapToProfile(response);
    } catch (e) {
      throw Exception('Failed to get profile: $e');
    }
  }

  /// Check if user has completed onboarding
  Future<bool> hasCompletedOnboarding(String userId) async {
    try {
      final response = await SupabaseService.client
          .from('profiles')
          .select('onboarding_completed')
          .eq('id', userId)
          .maybeSingle();

      return response?['onboarding_completed'] ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Update specific profile fields
  Future<void> updateProfileFields(String userId, Map<String, dynamic> updates) async {
    try {
      await SupabaseService.client
          .from('profiles')
          .update({
            ...updates,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', userId);
    } catch (e) {
      throw Exception('Failed to update profile: $e');
    }
  }

  /// Convert UserProfile to Supabase map
  Map<String, dynamic> _profileToSupabaseMap(UserProfile profile) {
    return {
      'id': profile.id,
      'email': profile.email,
      'display_name': profile.displayName,
      'created_at': profile.createdAt.toIso8601String(),
      'updated_at': profile.updatedAt.toIso8601String(),
      
      // Personal Information
      'gender': profile.gender,
      'age': profile.age,
      'height': profile.height,
      'height_unit': profile.heightUnit,
      'weight': profile.weight,
      'weight_unit': profile.weightUnit,
      
      // Fitness Goals
      'fitness_goal_primary': profile.fitnessGoalPrimary,
      'fitness_goals_array': profile.fitnessGoalsArray,
      'fitness_goals_order': profile.fitnessGoalsOrder,
      'sport_of_choice': profile.sportOfChoice,
      'specific_sport_activity': profile.specificSportActivity,
      'additional_health_info': profile.additionalHealthInfo,
      
      // Fitness Experience & Levels
      'training_experience_level': profile.trainingExperienceLevel,
      'fitness_experience': profile.fitnessExperience,
      'fitness_level': profile.fitnessLevel,
      'cardio_fitness_level': profile.cardioFitnessLevel,
      'weightlifting_fitness_level': profile.weightliftingFitnessLevel,
      'cardiolevel': profile.cardioLevel,
      'cardio_level_description': profile.cardioLevelDescription,
      'weightliftinglevel': profile.weightliftingLevel,
      'weightlifting_level_description': profile.weightliftingLevelDescription,
      
      // Workout Preferences
      'workout_frequency_days': profile.workoutFrequencyDays,
      'preferred_workout_days_count': profile.preferredWorkoutDaysCount,
      'workout_days': profile.workoutDays,
      'workout_duration_preference': profile.workoutDurationPreference,
      'preferred_workout_duration': profile.preferredWorkoutDuration,
      
      // Exercise & Equipment
      'exercise_preferences': profile.exercisePreferences,
      'equipment': profile.equipment,
      'workout_environment': profile.workoutEnvironment,
      'sport_activity': profile.sportActivity,
      
      // Limitations & Restrictions
      'physical_limitations': profile.physicalLimitations,
      'health_conditions': profile.healthConditions,
      'exercises_to_avoid': profile.exercisesToAvoid,
      'excluded_exercises': profile.excludedExercises,
      
      // Nutrition & Lifestyle
      'diet_preferences': profile.dietPreferences,
      'dietary_restrictions': profile.dietaryRestrictions,
      'meals_per_day': profile.mealsPerDay,
      'caloric_goal': profile.caloricGoal,
      'taking_supplements': profile.takingSupplements,
      'supplements': profile.supplements,
      'sleep_quality': profile.sleepQuality,
      
      // Progress Tracking
      'onboarding_completed': profile.onboardingCompleted,
      'has_completed_preferences': profile.hasCompletedPreferences,
      'fitness_assessment_completed': profile.fitnessAssessmentCompleted,
      
      // Additional Data
      'fitness_guide': profile.fitnessGuide,
      'additional_notes': profile.additionalNotes,
      'metadata': profile.metadata,
    };
  }

  /// Convert Supabase map to UserProfile
  UserProfile _supabaseMapToProfile(Map<String, dynamic> data) {
    return UserProfile(
      id: data['id'],
      email: data['email'],
      displayName: data['display_name'],
      createdAt: DateTime.parse(data['created_at']),
      updatedAt: DateTime.parse(data['updated_at']),
      
      // Personal Information
      gender: data['gender'],
      age: data['age'],
      height: data['height']?.toDouble(),
      heightUnit: data['height_unit'] ?? 'cm',
      weight: data['weight']?.toDouble(),
      weightUnit: data['weight_unit'] ?? 'kg',
      
      // Fitness Goals
      fitnessGoalPrimary: data['fitness_goal_primary'],
      fitnessGoalsArray: _parseStringList(data['fitness_goals_array']),
      fitnessGoalsOrder: _parseStringList(data['fitness_goals_order']),
      sportOfChoice: data['sport_of_choice'],
      specificSportActivity: data['specific_sport_activity'],
      additionalHealthInfo: data['additional_health_info'],
      
      // Fitness Experience & Levels
      trainingExperienceLevel: data['training_experience_level'],
      fitnessExperience: data['fitness_experience'],
      fitnessLevel: data['fitness_level'],
      cardioFitnessLevel: data['cardio_fitness_level'],
      weightliftingFitnessLevel: data['weightlifting_fitness_level'],
      cardioLevel: data['cardiolevel'],
      cardioLevelDescription: data['cardio_level_description'],
      weightliftingLevel: data['weightliftinglevel'],
      weightliftingLevelDescription: data['weightlifting_level_description'],
      
      // Workout Preferences
      workoutFrequencyDays: data['workout_frequency_days'],
      preferredWorkoutDaysCount: data['preferred_workout_days_count'],
      workoutDays: _parseStringList(data['workout_days']),
      workoutDurationPreference: data['workout_duration_preference'],
      preferredWorkoutDuration: data['preferred_workout_duration'],
      
      // Exercise & Equipment
      exercisePreferences: _parseStringList(data['exercise_preferences']),
      equipment: _parseStringList(data['equipment']),
      workoutEnvironment: _parseStringList(data['workout_environment']),
      sportActivity: data['sport_activity'],
      
      // Limitations & Restrictions
      physicalLimitations: _parseStringList(data['physical_limitations']),
      healthConditions: _parseStringList(data['health_conditions']),
      exercisesToAvoid: _parseStringList(data['exercises_to_avoid']),
      excludedExercises: _parseStringList(data['excluded_exercises']),
      
      // Nutrition & Lifestyle
      dietPreferences: _parseStringList(data['diet_preferences']),
      dietaryRestrictions: _parseStringList(data['dietary_restrictions']),
      mealsPerDay: data['meals_per_day'],
      caloricGoal: data['caloric_goal'],
      takingSupplements: data['taking_supplements'],
      supplements: _parseStringList(data['supplements']),
      sleepQuality: data['sleep_quality'],
      
      // Progress Tracking
      onboardingCompleted: data['onboarding_completed'] ?? false,
      hasCompletedPreferences: data['has_completed_preferences'] ?? false,
      fitnessAssessmentCompleted: data['fitness_assessment_completed'] ?? false,
      
      // Additional Data
      fitnessGuide: data['fitness_guide'],
      additionalNotes: data['additional_notes'],
      metadata: data['metadata'],
    );
  }

  /// Parse string list from Supabase array
  List<String> _parseStringList(dynamic value) {
    if (value == null) return [];
    if (value is List) {
      return value.map((e) => e.toString()).toList();
    }
    return [];
  }
}
