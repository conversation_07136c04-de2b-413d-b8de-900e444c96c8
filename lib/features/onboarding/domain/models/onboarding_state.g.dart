// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'onboarding_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$OnboardingStateImpl _$$OnboardingStateImplFromJson(
        Map<String, dynamic> json) =>
    _$OnboardingStateImpl(
      currentPage: (json['currentPage'] as num?)?.toInt() ?? 0,
      totalPages: (json['totalPages'] as num?)?.toInt() ?? 7,
      isLoading: json['isLoading'] as bool? ?? false,
      isCompleted: json['isCompleted'] as bool? ?? false,
      error: json['error'] as String?,
      name: json['name'] as String?,
      gender: json['gender'] as String?,
      age: (json['age'] as num?)?.toInt(),
      height: (json['height'] as num?)?.toDouble(),
      heightUnit: json['heightUnit'] as String? ?? 'ft',
      weight: (json['weight'] as num?)?.toDouble(),
      weightUnit: json['weightUnit'] as String? ?? 'lbs',
      selectedGoals: (json['selectedGoals'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      primaryGoal: json['primaryGoal'] as String?,
      sportActivity: json['sportActivity'] as String?,
      additionalHealthInfo: json['additionalHealthInfo'] as String?,
      cardioLevel: (json['cardioLevel'] as num?)?.toInt(),
      weightliftingLevel: (json['weightliftingLevel'] as num?)?.toInt(),
      exercisesToAvoid: (json['exercisesToAvoid'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      workoutEnvironments: (json['workoutEnvironments'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      availableEquipment: (json['availableEquipment'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      workoutFrequency: (json['workoutFrequency'] as num?)?.toInt(),
      sessionDuration: json['sessionDuration'] as String?,
      preferredDays: (json['preferredDays'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      additionalNotes: json['additionalNotes'] as String?,
      healthConditions: (json['healthConditions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      dietaryRestrictions: (json['dietaryRestrictions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$OnboardingStateImplToJson(
        _$OnboardingStateImpl instance) =>
    <String, dynamic>{
      'currentPage': instance.currentPage,
      'totalPages': instance.totalPages,
      'isLoading': instance.isLoading,
      'isCompleted': instance.isCompleted,
      'error': instance.error,
      'name': instance.name,
      'gender': instance.gender,
      'age': instance.age,
      'height': instance.height,
      'heightUnit': instance.heightUnit,
      'weight': instance.weight,
      'weightUnit': instance.weightUnit,
      'selectedGoals': instance.selectedGoals,
      'primaryGoal': instance.primaryGoal,
      'sportActivity': instance.sportActivity,
      'additionalHealthInfo': instance.additionalHealthInfo,
      'cardioLevel': instance.cardioLevel,
      'weightliftingLevel': instance.weightliftingLevel,
      'exercisesToAvoid': instance.exercisesToAvoid,
      'workoutEnvironments': instance.workoutEnvironments,
      'availableEquipment': instance.availableEquipment,
      'workoutFrequency': instance.workoutFrequency,
      'sessionDuration': instance.sessionDuration,
      'preferredDays': instance.preferredDays,
      'additionalNotes': instance.additionalNotes,
      'healthConditions': instance.healthConditions,
      'dietaryRestrictions': instance.dietaryRestrictions,
    };
