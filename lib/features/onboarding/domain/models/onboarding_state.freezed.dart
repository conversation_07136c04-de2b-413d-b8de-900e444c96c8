// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'onboarding_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

OnboardingState _$OnboardingStateFromJson(Map<String, dynamic> json) {
  return _OnboardingState.fromJson(json);
}

/// @nodoc
mixin _$OnboardingState {
  int get currentPage => throw _privateConstructorUsedError;
  int get totalPages => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  bool get isCompleted => throw _privateConstructorUsedError;
  String? get error =>
      throw _privateConstructorUsedError; // Page 1: Personal Information
  String? get name => throw _privateConstructorUsedError;
  String? get gender => throw _privateConstructorUsedError;
  int? get age => throw _privateConstructorUsedError;
  double? get height => throw _privateConstructorUsedError;
  String get heightUnit => throw _privateConstructorUsedError;
  double? get weight => throw _privateConstructorUsedError;
  String get weightUnit =>
      throw _privateConstructorUsedError; // Page 2: Fitness Goals
  List<String> get selectedGoals => throw _privateConstructorUsedError;
  String? get primaryGoal => throw _privateConstructorUsedError;
  String? get sportActivity => throw _privateConstructorUsedError;
  String? get additionalHealthInfo =>
      throw _privateConstructorUsedError; // Page 3: Fitness Levels
  int? get cardioLevel => throw _privateConstructorUsedError;
  int? get weightliftingLevel => throw _privateConstructorUsedError;
  List<String> get exercisesToAvoid =>
      throw _privateConstructorUsedError; // Page 4: Workout Environment
  List<String> get workoutEnvironments => throw _privateConstructorUsedError;
  List<String> get availableEquipment =>
      throw _privateConstructorUsedError; // Page 5: Workout Schedule
  int? get workoutFrequency => throw _privateConstructorUsedError;
  String? get sessionDuration => throw _privateConstructorUsedError;
  List<String> get preferredDays =>
      throw _privateConstructorUsedError; // Page 6: Additional Information
  String? get additionalNotes => throw _privateConstructorUsedError;
  List<String> get healthConditions => throw _privateConstructorUsedError;
  List<String> get dietaryRestrictions => throw _privateConstructorUsedError;

  /// Serializes this OnboardingState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OnboardingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OnboardingStateCopyWith<OnboardingState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OnboardingStateCopyWith<$Res> {
  factory $OnboardingStateCopyWith(
          OnboardingState value, $Res Function(OnboardingState) then) =
      _$OnboardingStateCopyWithImpl<$Res, OnboardingState>;
  @useResult
  $Res call(
      {int currentPage,
      int totalPages,
      bool isLoading,
      bool isCompleted,
      String? error,
      String? name,
      String? gender,
      int? age,
      double? height,
      String heightUnit,
      double? weight,
      String weightUnit,
      List<String> selectedGoals,
      String? primaryGoal,
      String? sportActivity,
      String? additionalHealthInfo,
      int? cardioLevel,
      int? weightliftingLevel,
      List<String> exercisesToAvoid,
      List<String> workoutEnvironments,
      List<String> availableEquipment,
      int? workoutFrequency,
      String? sessionDuration,
      List<String> preferredDays,
      String? additionalNotes,
      List<String> healthConditions,
      List<String> dietaryRestrictions});
}

/// @nodoc
class _$OnboardingStateCopyWithImpl<$Res, $Val extends OnboardingState>
    implements $OnboardingStateCopyWith<$Res> {
  _$OnboardingStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OnboardingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentPage = null,
    Object? totalPages = null,
    Object? isLoading = null,
    Object? isCompleted = null,
    Object? error = freezed,
    Object? name = freezed,
    Object? gender = freezed,
    Object? age = freezed,
    Object? height = freezed,
    Object? heightUnit = null,
    Object? weight = freezed,
    Object? weightUnit = null,
    Object? selectedGoals = null,
    Object? primaryGoal = freezed,
    Object? sportActivity = freezed,
    Object? additionalHealthInfo = freezed,
    Object? cardioLevel = freezed,
    Object? weightliftingLevel = freezed,
    Object? exercisesToAvoid = null,
    Object? workoutEnvironments = null,
    Object? availableEquipment = null,
    Object? workoutFrequency = freezed,
    Object? sessionDuration = freezed,
    Object? preferredDays = null,
    Object? additionalNotes = freezed,
    Object? healthConditions = null,
    Object? dietaryRestrictions = null,
  }) {
    return _then(_value.copyWith(
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      totalPages: null == totalPages
          ? _value.totalPages
          : totalPages // ignore: cast_nullable_to_non_nullable
              as int,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isCompleted: null == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      gender: freezed == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
      age: freezed == age
          ? _value.age
          : age // ignore: cast_nullable_to_non_nullable
              as int?,
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double?,
      heightUnit: null == heightUnit
          ? _value.heightUnit
          : heightUnit // ignore: cast_nullable_to_non_nullable
              as String,
      weight: freezed == weight
          ? _value.weight
          : weight // ignore: cast_nullable_to_non_nullable
              as double?,
      weightUnit: null == weightUnit
          ? _value.weightUnit
          : weightUnit // ignore: cast_nullable_to_non_nullable
              as String,
      selectedGoals: null == selectedGoals
          ? _value.selectedGoals
          : selectedGoals // ignore: cast_nullable_to_non_nullable
              as List<String>,
      primaryGoal: freezed == primaryGoal
          ? _value.primaryGoal
          : primaryGoal // ignore: cast_nullable_to_non_nullable
              as String?,
      sportActivity: freezed == sportActivity
          ? _value.sportActivity
          : sportActivity // ignore: cast_nullable_to_non_nullable
              as String?,
      additionalHealthInfo: freezed == additionalHealthInfo
          ? _value.additionalHealthInfo
          : additionalHealthInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      cardioLevel: freezed == cardioLevel
          ? _value.cardioLevel
          : cardioLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      weightliftingLevel: freezed == weightliftingLevel
          ? _value.weightliftingLevel
          : weightliftingLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      exercisesToAvoid: null == exercisesToAvoid
          ? _value.exercisesToAvoid
          : exercisesToAvoid // ignore: cast_nullable_to_non_nullable
              as List<String>,
      workoutEnvironments: null == workoutEnvironments
          ? _value.workoutEnvironments
          : workoutEnvironments // ignore: cast_nullable_to_non_nullable
              as List<String>,
      availableEquipment: null == availableEquipment
          ? _value.availableEquipment
          : availableEquipment // ignore: cast_nullable_to_non_nullable
              as List<String>,
      workoutFrequency: freezed == workoutFrequency
          ? _value.workoutFrequency
          : workoutFrequency // ignore: cast_nullable_to_non_nullable
              as int?,
      sessionDuration: freezed == sessionDuration
          ? _value.sessionDuration
          : sessionDuration // ignore: cast_nullable_to_non_nullable
              as String?,
      preferredDays: null == preferredDays
          ? _value.preferredDays
          : preferredDays // ignore: cast_nullable_to_non_nullable
              as List<String>,
      additionalNotes: freezed == additionalNotes
          ? _value.additionalNotes
          : additionalNotes // ignore: cast_nullable_to_non_nullable
              as String?,
      healthConditions: null == healthConditions
          ? _value.healthConditions
          : healthConditions // ignore: cast_nullable_to_non_nullable
              as List<String>,
      dietaryRestrictions: null == dietaryRestrictions
          ? _value.dietaryRestrictions
          : dietaryRestrictions // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OnboardingStateImplCopyWith<$Res>
    implements $OnboardingStateCopyWith<$Res> {
  factory _$$OnboardingStateImplCopyWith(_$OnboardingStateImpl value,
          $Res Function(_$OnboardingStateImpl) then) =
      __$$OnboardingStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int currentPage,
      int totalPages,
      bool isLoading,
      bool isCompleted,
      String? error,
      String? name,
      String? gender,
      int? age,
      double? height,
      String heightUnit,
      double? weight,
      String weightUnit,
      List<String> selectedGoals,
      String? primaryGoal,
      String? sportActivity,
      String? additionalHealthInfo,
      int? cardioLevel,
      int? weightliftingLevel,
      List<String> exercisesToAvoid,
      List<String> workoutEnvironments,
      List<String> availableEquipment,
      int? workoutFrequency,
      String? sessionDuration,
      List<String> preferredDays,
      String? additionalNotes,
      List<String> healthConditions,
      List<String> dietaryRestrictions});
}

/// @nodoc
class __$$OnboardingStateImplCopyWithImpl<$Res>
    extends _$OnboardingStateCopyWithImpl<$Res, _$OnboardingStateImpl>
    implements _$$OnboardingStateImplCopyWith<$Res> {
  __$$OnboardingStateImplCopyWithImpl(
      _$OnboardingStateImpl _value, $Res Function(_$OnboardingStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of OnboardingState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentPage = null,
    Object? totalPages = null,
    Object? isLoading = null,
    Object? isCompleted = null,
    Object? error = freezed,
    Object? name = freezed,
    Object? gender = freezed,
    Object? age = freezed,
    Object? height = freezed,
    Object? heightUnit = null,
    Object? weight = freezed,
    Object? weightUnit = null,
    Object? selectedGoals = null,
    Object? primaryGoal = freezed,
    Object? sportActivity = freezed,
    Object? additionalHealthInfo = freezed,
    Object? cardioLevel = freezed,
    Object? weightliftingLevel = freezed,
    Object? exercisesToAvoid = null,
    Object? workoutEnvironments = null,
    Object? availableEquipment = null,
    Object? workoutFrequency = freezed,
    Object? sessionDuration = freezed,
    Object? preferredDays = null,
    Object? additionalNotes = freezed,
    Object? healthConditions = null,
    Object? dietaryRestrictions = null,
  }) {
    return _then(_$OnboardingStateImpl(
      currentPage: null == currentPage
          ? _value.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      totalPages: null == totalPages
          ? _value.totalPages
          : totalPages // ignore: cast_nullable_to_non_nullable
              as int,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isCompleted: null == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      gender: freezed == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
      age: freezed == age
          ? _value.age
          : age // ignore: cast_nullable_to_non_nullable
              as int?,
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double?,
      heightUnit: null == heightUnit
          ? _value.heightUnit
          : heightUnit // ignore: cast_nullable_to_non_nullable
              as String,
      weight: freezed == weight
          ? _value.weight
          : weight // ignore: cast_nullable_to_non_nullable
              as double?,
      weightUnit: null == weightUnit
          ? _value.weightUnit
          : weightUnit // ignore: cast_nullable_to_non_nullable
              as String,
      selectedGoals: null == selectedGoals
          ? _value._selectedGoals
          : selectedGoals // ignore: cast_nullable_to_non_nullable
              as List<String>,
      primaryGoal: freezed == primaryGoal
          ? _value.primaryGoal
          : primaryGoal // ignore: cast_nullable_to_non_nullable
              as String?,
      sportActivity: freezed == sportActivity
          ? _value.sportActivity
          : sportActivity // ignore: cast_nullable_to_non_nullable
              as String?,
      additionalHealthInfo: freezed == additionalHealthInfo
          ? _value.additionalHealthInfo
          : additionalHealthInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      cardioLevel: freezed == cardioLevel
          ? _value.cardioLevel
          : cardioLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      weightliftingLevel: freezed == weightliftingLevel
          ? _value.weightliftingLevel
          : weightliftingLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      exercisesToAvoid: null == exercisesToAvoid
          ? _value._exercisesToAvoid
          : exercisesToAvoid // ignore: cast_nullable_to_non_nullable
              as List<String>,
      workoutEnvironments: null == workoutEnvironments
          ? _value._workoutEnvironments
          : workoutEnvironments // ignore: cast_nullable_to_non_nullable
              as List<String>,
      availableEquipment: null == availableEquipment
          ? _value._availableEquipment
          : availableEquipment // ignore: cast_nullable_to_non_nullable
              as List<String>,
      workoutFrequency: freezed == workoutFrequency
          ? _value.workoutFrequency
          : workoutFrequency // ignore: cast_nullable_to_non_nullable
              as int?,
      sessionDuration: freezed == sessionDuration
          ? _value.sessionDuration
          : sessionDuration // ignore: cast_nullable_to_non_nullable
              as String?,
      preferredDays: null == preferredDays
          ? _value._preferredDays
          : preferredDays // ignore: cast_nullable_to_non_nullable
              as List<String>,
      additionalNotes: freezed == additionalNotes
          ? _value.additionalNotes
          : additionalNotes // ignore: cast_nullable_to_non_nullable
              as String?,
      healthConditions: null == healthConditions
          ? _value._healthConditions
          : healthConditions // ignore: cast_nullable_to_non_nullable
              as List<String>,
      dietaryRestrictions: null == dietaryRestrictions
          ? _value._dietaryRestrictions
          : dietaryRestrictions // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OnboardingStateImpl implements _OnboardingState {
  const _$OnboardingStateImpl(
      {this.currentPage = 0,
      this.totalPages = 7,
      this.isLoading = false,
      this.isCompleted = false,
      this.error,
      this.name,
      this.gender,
      this.age,
      this.height,
      this.heightUnit = 'ft',
      this.weight,
      this.weightUnit = 'lbs',
      final List<String> selectedGoals = const [],
      this.primaryGoal,
      this.sportActivity,
      this.additionalHealthInfo,
      this.cardioLevel,
      this.weightliftingLevel,
      final List<String> exercisesToAvoid = const [],
      final List<String> workoutEnvironments = const [],
      final List<String> availableEquipment = const [],
      this.workoutFrequency,
      this.sessionDuration,
      final List<String> preferredDays = const [],
      this.additionalNotes,
      final List<String> healthConditions = const [],
      final List<String> dietaryRestrictions = const []})
      : _selectedGoals = selectedGoals,
        _exercisesToAvoid = exercisesToAvoid,
        _workoutEnvironments = workoutEnvironments,
        _availableEquipment = availableEquipment,
        _preferredDays = preferredDays,
        _healthConditions = healthConditions,
        _dietaryRestrictions = dietaryRestrictions;

  factory _$OnboardingStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$OnboardingStateImplFromJson(json);

  @override
  @JsonKey()
  final int currentPage;
  @override
  @JsonKey()
  final int totalPages;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isCompleted;
  @override
  final String? error;
// Page 1: Personal Information
  @override
  final String? name;
  @override
  final String? gender;
  @override
  final int? age;
  @override
  final double? height;
  @override
  @JsonKey()
  final String heightUnit;
  @override
  final double? weight;
  @override
  @JsonKey()
  final String weightUnit;
// Page 2: Fitness Goals
  final List<String> _selectedGoals;
// Page 2: Fitness Goals
  @override
  @JsonKey()
  List<String> get selectedGoals {
    if (_selectedGoals is EqualUnmodifiableListView) return _selectedGoals;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_selectedGoals);
  }

  @override
  final String? primaryGoal;
  @override
  final String? sportActivity;
  @override
  final String? additionalHealthInfo;
// Page 3: Fitness Levels
  @override
  final int? cardioLevel;
  @override
  final int? weightliftingLevel;
  final List<String> _exercisesToAvoid;
  @override
  @JsonKey()
  List<String> get exercisesToAvoid {
    if (_exercisesToAvoid is EqualUnmodifiableListView)
      return _exercisesToAvoid;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_exercisesToAvoid);
  }

// Page 4: Workout Environment
  final List<String> _workoutEnvironments;
// Page 4: Workout Environment
  @override
  @JsonKey()
  List<String> get workoutEnvironments {
    if (_workoutEnvironments is EqualUnmodifiableListView)
      return _workoutEnvironments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_workoutEnvironments);
  }

  final List<String> _availableEquipment;
  @override
  @JsonKey()
  List<String> get availableEquipment {
    if (_availableEquipment is EqualUnmodifiableListView)
      return _availableEquipment;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_availableEquipment);
  }

// Page 5: Workout Schedule
  @override
  final int? workoutFrequency;
  @override
  final String? sessionDuration;
  final List<String> _preferredDays;
  @override
  @JsonKey()
  List<String> get preferredDays {
    if (_preferredDays is EqualUnmodifiableListView) return _preferredDays;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_preferredDays);
  }

// Page 6: Additional Information
  @override
  final String? additionalNotes;
  final List<String> _healthConditions;
  @override
  @JsonKey()
  List<String> get healthConditions {
    if (_healthConditions is EqualUnmodifiableListView)
      return _healthConditions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_healthConditions);
  }

  final List<String> _dietaryRestrictions;
  @override
  @JsonKey()
  List<String> get dietaryRestrictions {
    if (_dietaryRestrictions is EqualUnmodifiableListView)
      return _dietaryRestrictions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_dietaryRestrictions);
  }

  @override
  String toString() {
    return 'OnboardingState(currentPage: $currentPage, totalPages: $totalPages, isLoading: $isLoading, isCompleted: $isCompleted, error: $error, name: $name, gender: $gender, age: $age, height: $height, heightUnit: $heightUnit, weight: $weight, weightUnit: $weightUnit, selectedGoals: $selectedGoals, primaryGoal: $primaryGoal, sportActivity: $sportActivity, additionalHealthInfo: $additionalHealthInfo, cardioLevel: $cardioLevel, weightliftingLevel: $weightliftingLevel, exercisesToAvoid: $exercisesToAvoid, workoutEnvironments: $workoutEnvironments, availableEquipment: $availableEquipment, workoutFrequency: $workoutFrequency, sessionDuration: $sessionDuration, preferredDays: $preferredDays, additionalNotes: $additionalNotes, healthConditions: $healthConditions, dietaryRestrictions: $dietaryRestrictions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OnboardingStateImpl &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.totalPages, totalPages) ||
                other.totalPages == totalPages) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isCompleted, isCompleted) ||
                other.isCompleted == isCompleted) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.heightUnit, heightUnit) ||
                other.heightUnit == heightUnit) &&
            (identical(other.weight, weight) || other.weight == weight) &&
            (identical(other.weightUnit, weightUnit) ||
                other.weightUnit == weightUnit) &&
            const DeepCollectionEquality()
                .equals(other._selectedGoals, _selectedGoals) &&
            (identical(other.primaryGoal, primaryGoal) ||
                other.primaryGoal == primaryGoal) &&
            (identical(other.sportActivity, sportActivity) ||
                other.sportActivity == sportActivity) &&
            (identical(other.additionalHealthInfo, additionalHealthInfo) ||
                other.additionalHealthInfo == additionalHealthInfo) &&
            (identical(other.cardioLevel, cardioLevel) ||
                other.cardioLevel == cardioLevel) &&
            (identical(other.weightliftingLevel, weightliftingLevel) ||
                other.weightliftingLevel == weightliftingLevel) &&
            const DeepCollectionEquality()
                .equals(other._exercisesToAvoid, _exercisesToAvoid) &&
            const DeepCollectionEquality()
                .equals(other._workoutEnvironments, _workoutEnvironments) &&
            const DeepCollectionEquality()
                .equals(other._availableEquipment, _availableEquipment) &&
            (identical(other.workoutFrequency, workoutFrequency) ||
                other.workoutFrequency == workoutFrequency) &&
            (identical(other.sessionDuration, sessionDuration) ||
                other.sessionDuration == sessionDuration) &&
            const DeepCollectionEquality()
                .equals(other._preferredDays, _preferredDays) &&
            (identical(other.additionalNotes, additionalNotes) ||
                other.additionalNotes == additionalNotes) &&
            const DeepCollectionEquality()
                .equals(other._healthConditions, _healthConditions) &&
            const DeepCollectionEquality()
                .equals(other._dietaryRestrictions, _dietaryRestrictions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        currentPage,
        totalPages,
        isLoading,
        isCompleted,
        error,
        name,
        gender,
        age,
        height,
        heightUnit,
        weight,
        weightUnit,
        const DeepCollectionEquality().hash(_selectedGoals),
        primaryGoal,
        sportActivity,
        additionalHealthInfo,
        cardioLevel,
        weightliftingLevel,
        const DeepCollectionEquality().hash(_exercisesToAvoid),
        const DeepCollectionEquality().hash(_workoutEnvironments),
        const DeepCollectionEquality().hash(_availableEquipment),
        workoutFrequency,
        sessionDuration,
        const DeepCollectionEquality().hash(_preferredDays),
        additionalNotes,
        const DeepCollectionEquality().hash(_healthConditions),
        const DeepCollectionEquality().hash(_dietaryRestrictions)
      ]);

  /// Create a copy of OnboardingState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OnboardingStateImplCopyWith<_$OnboardingStateImpl> get copyWith =>
      __$$OnboardingStateImplCopyWithImpl<_$OnboardingStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OnboardingStateImplToJson(
      this,
    );
  }
}

abstract class _OnboardingState implements OnboardingState {
  const factory _OnboardingState(
      {final int currentPage,
      final int totalPages,
      final bool isLoading,
      final bool isCompleted,
      final String? error,
      final String? name,
      final String? gender,
      final int? age,
      final double? height,
      final String heightUnit,
      final double? weight,
      final String weightUnit,
      final List<String> selectedGoals,
      final String? primaryGoal,
      final String? sportActivity,
      final String? additionalHealthInfo,
      final int? cardioLevel,
      final int? weightliftingLevel,
      final List<String> exercisesToAvoid,
      final List<String> workoutEnvironments,
      final List<String> availableEquipment,
      final int? workoutFrequency,
      final String? sessionDuration,
      final List<String> preferredDays,
      final String? additionalNotes,
      final List<String> healthConditions,
      final List<String> dietaryRestrictions}) = _$OnboardingStateImpl;

  factory _OnboardingState.fromJson(Map<String, dynamic> json) =
      _$OnboardingStateImpl.fromJson;

  @override
  int get currentPage;
  @override
  int get totalPages;
  @override
  bool get isLoading;
  @override
  bool get isCompleted;
  @override
  String? get error; // Page 1: Personal Information
  @override
  String? get name;
  @override
  String? get gender;
  @override
  int? get age;
  @override
  double? get height;
  @override
  String get heightUnit;
  @override
  double? get weight;
  @override
  String get weightUnit; // Page 2: Fitness Goals
  @override
  List<String> get selectedGoals;
  @override
  String? get primaryGoal;
  @override
  String? get sportActivity;
  @override
  String? get additionalHealthInfo; // Page 3: Fitness Levels
  @override
  int? get cardioLevel;
  @override
  int? get weightliftingLevel;
  @override
  List<String> get exercisesToAvoid; // Page 4: Workout Environment
  @override
  List<String> get workoutEnvironments;
  @override
  List<String> get availableEquipment; // Page 5: Workout Schedule
  @override
  int? get workoutFrequency;
  @override
  String? get sessionDuration;
  @override
  List<String> get preferredDays; // Page 6: Additional Information
  @override
  String? get additionalNotes;
  @override
  List<String> get healthConditions;
  @override
  List<String> get dietaryRestrictions;

  /// Create a copy of OnboardingState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OnboardingStateImplCopyWith<_$OnboardingStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
