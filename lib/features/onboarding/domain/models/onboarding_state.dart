import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../profile/domain/models/user_profile.dart';

part 'onboarding_state.freezed.dart';
part 'onboarding_state.g.dart';

/// Onboarding flow state management
@freezed
class OnboardingState with _$OnboardingState {
  const factory OnboardingState({
    @Default(0) int currentPage,
    @Default(7) int totalPages,
    @Default(false) bool isLoading,
    @Default(false) bool isCompleted,
    String? error,
    
    // Page 1: Personal Information
    String? name,
    String? gender,
    int? age,
    double? height,
    @Default('ft') String heightUnit,
    double? weight,
    @Default('lbs') String weightUnit,
    
    // Page 2: Fitness Goals
    @Default([]) List<String> selectedGoals,
    String? primaryGoal,
    String? sportActivity,
    String? additionalHealthInfo,
    
    // Page 3: Fitness Levels
    int? cardioLevel,
    int? weightliftingLevel,
    @Default([]) List<String> exercisesToAvoid,
    
    // Page 4: Workout Environment
    @Default([]) List<String> workoutEnvironments,
    @Default([]) List<String> availableEquipment,
    
    // Page 5: Workout Schedule
    int? workoutFrequency,
    String? sessionDuration,
    @Default([]) List<String> preferredDays,
    
    // Page 6: Additional Information
    String? additionalNotes,
    @Default([]) List<String> healthConditions,
    @Default([]) List<String> dietaryRestrictions,
  }) = _OnboardingState;

  factory OnboardingState.fromJson(Map<String, dynamic> json) =>
      _$OnboardingStateFromJson(json);
}

/// Extension methods for OnboardingState
extension OnboardingStateExtensions on OnboardingState {
  /// Check if current page is valid to proceed
  bool get canProceedFromCurrentPage {
    switch (currentPage) {
      case 0: // Personal Information
        return name != null &&
               name!.isNotEmpty &&
               gender != null &&
               age != null &&
               height != null &&
               weight != null;
      case 1: // Fitness Goals
        return selectedGoals.isNotEmpty;
      case 2: // Fitness Levels
        return cardioLevel != null && weightliftingLevel != null;
      case 3: // Workout Environment
        return workoutEnvironments.isNotEmpty;
      case 4: // Workout Schedule
        return workoutFrequency != null && sessionDuration != null;
      case 5: // Additional Information
        return true; // Optional page
      case 6: // Review
        return true;
      default:
        return false;
    }
  }

  /// Get completion percentage for current page
  double get currentPageCompletion {
    switch (currentPage) {
      case 0: // Personal Information
        int completed = 0;
        int total = 6;
        if (name != null && name!.isNotEmpty) completed++;
        if (gender != null) completed++;
        if (age != null) completed++;
        if (height != null) completed++;
        if (weight != null) completed++;
        if (heightUnit.isNotEmpty && weightUnit.isNotEmpty) completed++;
        return completed / total;

      case 1: // Fitness Goals
        return selectedGoals.isEmpty ? 0.0 : 1.0;

      case 2: // Fitness Levels
        int completed = 0;
        int total = 2;
        if (cardioLevel != null) completed++;
        if (weightliftingLevel != null) completed++;
        return completed / total;

      case 3: // Workout Environment
        return workoutEnvironments.isEmpty ? 0.0 : 1.0;

      case 4: // Workout Schedule
        int completed = 0;
        int total = 2;
        if (workoutFrequency != null) completed++;
        if (sessionDuration != null) completed++;
        return completed / total;

      case 5: // Additional Information
        return 1.0; // Always complete since it's optional

      case 6: // Review
        return 1.0;

      default:
        return 0.0;
    }
  }

  /// Get overall completion percentage
  double get overallCompletion {
    return (currentPage + currentPageCompletion) / totalPages;
  }

  /// Convert to UserProfile for saving
  UserProfile toUserProfile(String userId) {
    return UserProfile(
      id: userId,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),

      // Personal Information
      displayName: name,
      gender: gender,
      age: age,
      height: height,
      heightUnit: heightUnit,
      weight: weight,
      weightUnit: weightUnit,

      // Fitness Goals
      fitnessGoalsArray: selectedGoals,
      fitnessGoalPrimary: primaryGoal ?? (selectedGoals.isNotEmpty ? selectedGoals.first : null),
      sportOfChoice: sportActivity,
      additionalHealthInfo: additionalHealthInfo,

      // Fitness Levels
      cardioFitnessLevel: cardioLevel,
      weightliftingFitnessLevel: weightliftingLevel,
      exercisesToAvoid: exercisesToAvoid,

      // Workout Environment & Equipment
      workoutEnvironment: workoutEnvironments,
      equipment: availableEquipment,

      // Workout Schedule
      workoutFrequencyDays: workoutFrequency,
      preferredWorkoutDuration: sessionDuration,
      workoutDays: preferredDays,

      // Additional Information
      additionalNotes: additionalNotes,
      healthConditions: healthConditions,
      dietaryRestrictions: dietaryRestrictions,

      // Completion flags
      onboardingCompleted: isCompleted,
      hasCompletedPreferences: true,
      fitnessAssessmentCompleted: cardioLevel != null && weightliftingLevel != null,
    );
  }

  /// Get page title
  String get currentPageTitle {
    switch (currentPage) {
      case 0:
        return "Let's Get to Know You";
      case 1:
        return "What Are Your Fitness Goals?";
      case 2:
        return "Your Current Fitness Level";
      case 3:
        return "Where Will You Work Out?";
      case 4:
        return "Your Ideal Workout Schedule";
      case 5:
        return "Anything Else?";
      case 6:
        return "Review Your Profile";
      default:
        return "FitPulse Onboarding";
    }
  }

  /// Get page subtitle
  String get currentPageSubtitle {
    switch (currentPage) {
      case 0:
        return "This information helps us create your personalized fitness journey";
      case 1:
        return "Select all that apply and arrange them in your preferred order";
      case 2:
        return "Be honest - this helps us start at the right intensity";
      case 3:
        return "Select all that apply";
      case 4:
        return "We'll create a plan that fits your life";
      case 5:
        return "Help us perfect your personalized plan";
      case 6:
        return "Review your information and generate your plan";
      default:
        return "Complete your fitness profile";
    }
  }
}
