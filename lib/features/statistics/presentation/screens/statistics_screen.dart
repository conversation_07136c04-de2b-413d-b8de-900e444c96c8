import 'package:flutter/material.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../../../shared/widgets/animated_charts.dart';
import '../../../../core/animations/counting_animation.dart';
import '../../../../core/theme/color_palette.dart';

class StatisticsScreen extends StatelessWidget {
  const StatisticsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    final weeklyData = [
      const ChartData(label: 'Mon', value: 45),
      const ChartData(label: 'Tue', value: 60),
      const ChartData(label: 'Wed', value: 30),
      const ChartData(label: 'Thu', value: 80),
      const ChartData(label: 'Fri', value: 55),
      const ChartData(label: 'Sat', value: 90),
      const ChartData(label: 'Sun', value: 70),
    ];

    final monthlyData = [
      const ChartData(label: 'Week 1', value: 320),
      const ChartData(label: 'Week 2', value: 280),
      const ChartData(label: 'Week 3', value: 450),
      const ChartData(label: 'Week 4', value: 380),
    ];
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Statistics'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Summary Stats
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Total Workouts',
                    '127',
                    Icons.fitness_center,
                    AppColorPalette.primaryOrange,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    context,
                    'Hours Trained',
                    '89.5',
                    Icons.timer,
                    AppColorPalette.accentBlue,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Weekly Activity Chart
            GlassMorphismCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Weekly Activity (Minutes)',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 20),
                  AnimatedBarChart(
                    data: weeklyData,
                    height: 200,
                    barColor: AppColorPalette.primaryOrange,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Monthly Progress
            GlassMorphismCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Monthly Progress',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 20),
                  AnimatedLineChart(
                    data: monthlyData,
                    height: 180,
                    lineColor: AppColorPalette.accentBlue,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Progress Rings
            GlassMorphismCard(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'This Week\'s Goals',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildProgressRing(
                        context,
                        'Workouts',
                        0.75,
                        '6/8',
                        AppColorPalette.primaryOrange,
                      ),
                      _buildProgressRing(
                        context,
                        'Calories',
                        0.60,
                        '1,800',
                        AppColorPalette.accentBlue,
                      ),
                      _buildProgressRing(
                        context,
                        'Minutes',
                        0.85,
                        '340',
                        AppColorPalette.successGreen,
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 100), // Bottom padding for navigation
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return GlassMorphismCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: 24,
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Icon(
                  Icons.trending_up,
                  color: color,
                  size: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          CountingAnimation(
            value: double.parse(value.replaceAll(',', '')),
            textStyle: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.w700,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildProgressRing(
    BuildContext context,
    String label,
    double progress,
    String value,
    Color color,
  ) {
    return Column(
      children: [
        AnimatedCircularProgress(
          progress: progress,
          size: 80,
          strokeWidth: 6,
          progressColor: color,
          center: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                value,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }
}
