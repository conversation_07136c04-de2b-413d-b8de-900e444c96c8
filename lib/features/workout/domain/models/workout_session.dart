class WorkoutSession {
  final String id;
  final String name;
  final String description;
  final int estimatedDuration; // in minutes
  final int estimatedCalories;
  final int totalSets;
  final List<WorkoutExercise> exercises;
  final String? backgroundImageUrl;
  final WorkoutStatus status;
  final DateTime? startedAt;
  final DateTime? completedAt;
  final int currentExerciseIndex;
  final int currentSetIndex;
  final Map<String, dynamic> sessionData;

  const WorkoutSession({
    required this.id,
    required this.name,
    required this.description,
    required this.estimatedDuration,
    required this.estimatedCalories,
    required this.totalSets,
    required this.exercises,
    this.backgroundImageUrl,
    this.status = WorkoutStatus.ready,
    this.startedAt,
    this.completedAt,
    this.currentExerciseIndex = 0,
    this.currentSetIndex = 0,
    this.sessionData = const {},
  });

  factory WorkoutSession.fromJson(Map<String, dynamic> json) {
    return WorkoutSession(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      estimatedDuration: json['estimatedDuration'] ?? 0,
      estimatedCalories: json['estimatedCalories'] ?? 0,
      totalSets: json['totalSets'] ?? 0,
      exercises: (json['exercises'] as List<dynamic>?)
          ?.map((e) => WorkoutExercise.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      backgroundImageUrl: json['backgroundImageUrl'],
      status: WorkoutStatus.values.firstWhere(
        (e) => e.toString() == 'WorkoutStatus.${json['status']}',
        orElse: () => WorkoutStatus.ready,
      ),
      startedAt: json['startedAt'] != null ? DateTime.parse(json['startedAt']) : null,
      completedAt: json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null,
      currentExerciseIndex: json['currentExerciseIndex'] ?? 0,
      currentSetIndex: json['currentSetIndex'] ?? 0,
      sessionData: Map<String, dynamic>.from(json['sessionData'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'estimatedDuration': estimatedDuration,
      'estimatedCalories': estimatedCalories,
      'totalSets': totalSets,
      'exercises': exercises.map((e) => e.toJson()).toList(),
      'backgroundImageUrl': backgroundImageUrl,
      'status': status.toString().split('.').last,
      'startedAt': startedAt?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'currentExerciseIndex': currentExerciseIndex,
      'currentSetIndex': currentSetIndex,
      'sessionData': sessionData,
    };
  }
}

class WorkoutExercise {
  final String id;
  final String name;
  final String description;
  final int sets;
  final List<int> targetReps;
  final List<double> targetWeights; // in lbs (imperial)
  final List<int>? completedReps;
  final List<double>? completedWeights;
  final String? videoUrl;
  final String? thumbnailUrl;
  final String? instructions;
  final int restInterval; // in seconds
  final bool isCompleted;
  final int completedSets;

  const WorkoutExercise({
    required this.id,
    required this.name,
    required this.description,
    required this.sets,
    required this.targetReps,
    required this.targetWeights,
    this.completedReps,
    this.completedWeights,
    this.videoUrl,
    this.thumbnailUrl,
    this.instructions,
    this.restInterval = 60,
    this.isCompleted = false,
    this.completedSets = 0,
  });

  factory WorkoutExercise.fromJson(Map<String, dynamic> json) {
    return WorkoutExercise(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      sets: json['sets'] ?? 3,
      targetReps: (json['targetReps'] as List<dynamic>?)?.cast<int>() ?? [10],
      targetWeights: (json['targetWeights'] as List<dynamic>?)?.cast<double>() ?? [0.0],
      completedReps: (json['completedReps'] as List<dynamic>?)?.cast<int>(),
      completedWeights: (json['completedWeights'] as List<dynamic>?)?.cast<double>(),
      videoUrl: json['videoUrl'],
      thumbnailUrl: json['thumbnailUrl'],
      instructions: json['instructions'],
      restInterval: json['restInterval'] ?? 60,
      isCompleted: json['isCompleted'] ?? false,
      completedSets: json['completedSets'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'sets': sets,
      'targetReps': targetReps,
      'targetWeights': targetWeights,
      'completedReps': completedReps,
      'completedWeights': completedWeights,
      'videoUrl': videoUrl,
      'thumbnailUrl': thumbnailUrl,
      'instructions': instructions,
      'restInterval': restInterval,
      'isCompleted': isCompleted,
      'completedSets': completedSets,
    };
  }
}

enum WorkoutStatus {
  ready,
  inProgress,
  paused,
  completed,
  cancelled,
}

/// Extension for WorkoutSession to provide formatted values and utilities
extension WorkoutSessionExtension on WorkoutSession {
  /// Get duration text in imperial format
  String get durationText => '$estimatedDuration min';

  /// Get calories text in imperial format
  String get caloriesText => '$estimatedCalories kcal';

  /// Get sets text with proper pluralization
  String get setsText {
    if (totalSets == 1) return '1 set';
    return '$totalSets sets';
  }

  /// Get exercises text with proper pluralization
  String get exercisesText {
    if (exercises.length == 1) return '1 exercise';
    return '${exercises.length} exercises';
  }

  /// Get completion percentage
  double get completionPercentage {
    if (exercises.isEmpty) return 0.0;
    final completedExercises = exercises.where((e) => e.isCompleted).length;
    return completedExercises / exercises.length;
  }

  /// Get current exercise
  WorkoutExercise? get currentExercise {
    if (currentExerciseIndex >= exercises.length) return null;
    return exercises[currentExerciseIndex];
  }

  /// Get next exercise to perform
  WorkoutExercise? get nextExercise {
    final nextIndex = currentExerciseIndex + 1;
    if (nextIndex >= exercises.length) return null;
    return exercises[nextIndex];
  }

  /// Check if workout can be started
  bool get canStart => status == WorkoutStatus.ready;

  /// Check if workout can be resumed
  bool get canResume => status == WorkoutStatus.paused;

  /// Check if workout is active
  bool get isActive => status == WorkoutStatus.inProgress;

  /// Check if workout is completed
  bool get isCompleted => status == WorkoutStatus.completed;

  /// Get status text for display
  String get statusText {
    switch (status) {
      case WorkoutStatus.ready:
        return 'Ready to Start';
      case WorkoutStatus.inProgress:
        return 'In Progress';
      case WorkoutStatus.paused:
        return 'Paused';
      case WorkoutStatus.completed:
        return 'Completed';
      case WorkoutStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Get motivational start message
  String get startMessage {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Start your day strong! 💪';
    } else if (hour < 17) {
      return 'Power through your workout! 🔥';
    } else {
      return 'Finish strong today! ⚡';
    }
  }

  /// Get elapsed time in minutes
  int get elapsedMinutes {
    if (startedAt == null) return 0;
    final now = completedAt ?? DateTime.now();
    return now.difference(startedAt!).inMinutes;
  }

  /// Get remaining exercises count
  int get remainingExercises {
    return exercises.where((e) => !e.isCompleted).length;
  }

  /// Get total completed sets
  int get totalCompletedSets {
    return exercises.fold<int>(
      0,
      (sum, exercise) => sum + exercise.completedSets,
    );
  }
}

/// Extension for WorkoutExercise to provide formatted values and utilities
extension WorkoutExerciseExtension on WorkoutExercise {
  /// Get sets text with proper pluralization
  String get setsText {
    if (sets == 1) return '1 set';
    return '$sets sets';
  }

  /// Get completion percentage for this exercise
  double get completionPercentage {
    if (sets == 0) return 0.0;
    return completedSets / sets;
  }

  /// Get remaining sets
  int get remainingSets => sets - completedSets;

  /// Get current set number (1-based)
  int get currentSetNumber => completedSets + 1;

  /// Check if exercise has next set
  bool get hasNextSet => completedSets < sets;

  /// Get target reps for current set
  int get currentTargetReps {
    if (targetReps.isEmpty) return 10;
    final index = completedSets.clamp(0, targetReps.length - 1);
    return targetReps[index];
  }

  /// Get target weight for current set
  double get currentTargetWeight {
    if (targetWeights.isEmpty) return 0.0;
    final index = completedSets.clamp(0, targetWeights.length - 1);
    return targetWeights[index];
  }

  /// Get formatted weight text
  String get currentWeightText {
    final weight = currentTargetWeight;
    if (weight == 0.0) return 'Bodyweight';
    return '${weight.toInt()} lbs';
  }

  /// Get rest interval text
  String get restIntervalText {
    if (restInterval < 60) {
      return '${restInterval}s rest';
    } else {
      final minutes = restInterval ~/ 60;
      final seconds = restInterval % 60;
      if (seconds == 0) {
        return '${minutes}m rest';
      } else {
        return '${minutes}m ${seconds}s rest';
      }
    }
  }

  /// Check if this is the last set
  bool get isLastSet => currentSetNumber >= sets;

  /// Get progress text for display
  String get progressText => 'Set $currentSetNumber of $sets';
}
