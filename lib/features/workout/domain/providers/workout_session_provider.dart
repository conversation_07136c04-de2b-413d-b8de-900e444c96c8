import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/services/supabase_service.dart';
import '../models/workout_session.dart';
import '../../../dashboard/domain/models/today_workout.dart';

/// Workout session state
class WorkoutSessionState {
  final String? workoutLogId;
  final WorkoutSession? currentWorkout;
  final int currentExerciseIndex;
  final int currentSetIndex;
  final Duration elapsedTime;
  final WorkoutFlowStatus flowStatus;
  final bool isLoading;
  final String? error;
  final DateTime? startTime;
  final DateTime? lastSaveTime;
  final Map<String, dynamic> sessionData;

  const WorkoutSessionState({
    this.workoutLogId,
    this.currentWorkout,
    this.currentExerciseIndex = 0,
    this.currentSetIndex = 0,
    this.elapsedTime = Duration.zero,
    this.flowStatus = WorkoutFlowStatus.ready,
    this.isLoading = false,
    this.error,
    this.startTime,
    this.lastSaveTime,
    this.sessionData = const {},
  });

  WorkoutSessionState copyWith({
    String? workoutLogId,
    WorkoutSession? currentWorkout,
    int? currentExerciseIndex,
    int? currentSetIndex,
    Duration? elapsedTime,
    WorkoutFlowStatus? flowStatus,
    bool? isLoading,
    String? error,
    DateTime? startTime,
    DateTime? lastSaveTime,
    Map<String, dynamic>? sessionData,
  }) {
    return WorkoutSessionState(
      workoutLogId: workoutLogId ?? this.workoutLogId,
      currentWorkout: currentWorkout ?? this.currentWorkout,
      currentExerciseIndex: currentExerciseIndex ?? this.currentExerciseIndex,
      currentSetIndex: currentSetIndex ?? this.currentSetIndex,
      elapsedTime: elapsedTime ?? this.elapsedTime,
      flowStatus: flowStatus ?? this.flowStatus,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      startTime: startTime ?? this.startTime,
      lastSaveTime: lastSaveTime ?? this.lastSaveTime,
      sessionData: sessionData ?? this.sessionData,
    );
  }
}

/// Workout flow status enum
enum WorkoutFlowStatus {
  ready,
  loading,
  preFlightChecks,
  countdown,
  active,
  resting,
  paused,
  completed,
  cancelled,
  error,
}

/// Workout session notifier
class WorkoutSessionNotifier extends StateNotifier<WorkoutSessionState> {
  WorkoutSessionNotifier() : super(const WorkoutSessionState());

  Timer? _autoSaveTimer;
  Timer? _workoutTimer;

  @override
  void dispose() {
    _autoSaveTimer?.cancel();
    _workoutTimer?.cancel();
    super.dispose();
  }

  /// Start the complete workout flow
  Future<void> startWorkoutFlow(TodayWorkout todayWorkout) async {
    try {
      state = state.copyWith(
        isLoading: true,
        flowStatus: WorkoutFlowStatus.loading,
        error: null,
      );

      // Step 1: Pre-flight checks
      await _performPreFlightChecks(todayWorkout);

      // Step 2: Create workout log entry
      final workoutLogId = await _createWorkoutLog(todayWorkout.id);

      // Step 3: Check for incomplete sessions
      await _checkIncompleteSession();

      // Step 4: Convert TodayWorkout to WorkoutSession
      final workoutSession = await _convertToWorkoutSession(todayWorkout);

      // Step 5: Preload exercise data
      await _preloadExerciseData(workoutSession);

      state = state.copyWith(
        workoutLogId: workoutLogId,
        currentWorkout: workoutSession,
        flowStatus: WorkoutFlowStatus.countdown,
        isLoading: false,
        startTime: DateTime.now(),
      );

      // Start auto-save timer
      _startAutoSave();

    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        flowStatus: WorkoutFlowStatus.error,
        error: e.toString(),
      );
      rethrow;
    }
  }

  /// Perform pre-flight checks
  Future<void> _performPreFlightChecks(TodayWorkout workout) async {
    state = state.copyWith(flowStatus: WorkoutFlowStatus.preFlightChecks);

    // Check device storage space
    // Check network connectivity
    // Initialize motion/health tracking
    // Set screen brightness
    // Disable auto-lock

    await Future.delayed(const Duration(milliseconds: 500));
  }

  /// Create workout log entry in database
  Future<String> _createWorkoutLog(String workoutId) async {
    final userId = SupabaseService.currentUser?.id;
    if (userId == null) {
      throw Exception('User not authenticated');
    }

    final response = await SupabaseService.client
        .from('workout_logs')
        .insert({
          'user_id': userId,
          'workout_id': workoutId,
          'started_at': DateTime.now().toIso8601String(),
          'status': 'in_progress',
        })
        .select('id')
        .single();

    return response['id'];
  }

  /// Check for incomplete sessions
  Future<void> _checkIncompleteSession() async {
    final userId = SupabaseService.currentUser?.id;
    if (userId == null) return;

    final incompleteSession = await SupabaseService.client
        .from('workout_logs')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'in_progress')
        .maybeSingle();

    if (incompleteSession != null) {
      // Handle resume scenario
      // For now, we'll continue with new session
      debugPrint('Found incomplete session: ${incompleteSession['id']}');
    }
  }

  /// Convert TodayWorkout to WorkoutSession
  Future<WorkoutSession> _convertToWorkoutSession(TodayWorkout todayWorkout) async {
    // Convert TodayWorkoutExercise to WorkoutExercise
    final convertedExercises = todayWorkout.exercises.map((todayExercise) {
      return WorkoutExercise(
        id: todayExercise.id,
        name: todayExercise.name,
        description: todayExercise.description,
        sets: todayExercise.sets,
        targetReps: todayExercise.reps, // Convert reps to targetReps
        targetWeights: todayExercise.weights, // Convert weights to targetWeights
        videoUrl: todayExercise.videoUrl,
        thumbnailUrl: todayExercise.thumbnailUrl,
        instructions: todayExercise.instructions,
        restInterval: todayExercise.restInterval,
        isCompleted: todayExercise.isCompleted,
      );
    }).toList();

    return WorkoutSession(
      id: todayWorkout.id,
      name: todayWorkout.name,
      description: todayWorkout.description,
      estimatedDuration: todayWorkout.estimatedDuration,
      estimatedCalories: todayWorkout.estimatedCalories,
      totalSets: todayWorkout.totalSets,
      exercises: convertedExercises,
      status: WorkoutStatus.ready,
    );
  }

  /// Preload exercise data (videos, images)
  Future<void> _preloadExerciseData(WorkoutSession workout) async {
    // Preload first 3 exercise videos
    final exercisesToPreload = workout.exercises.take(3);
    
    for (final exercise in exercisesToPreload) {
      if (exercise.videoUrl != null) {
        // Preload video
        debugPrint('Preloading video for: ${exercise.name}');
      }
    }
  }

  /// Start auto-save timer
  void _startAutoSave() {
    _autoSaveTimer?.cancel();
    _autoSaveTimer = Timer.periodic(const Duration(seconds: 30), (_) {
      _saveWorkoutState();
    });
  }

  /// Save workout state to database
  Future<void> _saveWorkoutState() async {
    if (state.workoutLogId == null) return;

    try {
      await SupabaseService.client
          .from('workout_logs')
          .update({
            'current_exercise_index': state.currentExerciseIndex,
            'current_set_index': state.currentSetIndex,
            'elapsed_time': state.elapsedTime.inSeconds,
            'session_data': jsonEncode(state.sessionData),
            'last_updated': DateTime.now().toIso8601String(),
          })
          .eq('id', state.workoutLogId!);

      state = state.copyWith(lastSaveTime: DateTime.now());
    } catch (e) {
      debugPrint('Failed to save workout state: $e');
    }
  }

  /// Start active workout
  void startActiveWorkout() {
    state = state.copyWith(
      flowStatus: WorkoutFlowStatus.active,
      startTime: DateTime.now(),
    );

    _startWorkoutTimer();
  }

  /// Start workout timer
  void _startWorkoutTimer() {
    _workoutTimer?.cancel();
    _workoutTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (state.flowStatus == WorkoutFlowStatus.active) {
        state = state.copyWith(
          elapsedTime: state.elapsedTime + const Duration(seconds: 1),
        );
      }
    });
  }

  /// Pause workout
  void pauseWorkout() {
    state = state.copyWith(flowStatus: WorkoutFlowStatus.paused);
    _workoutTimer?.cancel();
  }

  /// Resume workout
  void resumeWorkout() {
    state = state.copyWith(flowStatus: WorkoutFlowStatus.active);
    _startWorkoutTimer();
  }

  /// Complete workout
  Future<void> completeWorkout() async {
    state = state.copyWith(flowStatus: WorkoutFlowStatus.completed);
    _workoutTimer?.cancel();
    _autoSaveTimer?.cancel();

    if (state.workoutLogId != null) {
      await SupabaseService.client
          .from('workout_logs')
          .update({
            'status': 'completed',
            'completed_at': DateTime.now().toIso8601String(),
            'total_duration': state.elapsedTime.inSeconds,
          })
          .eq('id', state.workoutLogId!);
    }
  }

  /// Cancel workout
  Future<void> cancelWorkout() async {
    state = state.copyWith(flowStatus: WorkoutFlowStatus.cancelled);
    _workoutTimer?.cancel();
    _autoSaveTimer?.cancel();

    if (state.workoutLogId != null) {
      await SupabaseService.client
          .from('workout_logs')
          .update({
            'status': 'cancelled',
            'cancelled_at': DateTime.now().toIso8601String(),
          })
          .eq('id', state.workoutLogId!);
    }
  }

  /// Move to next exercise
  void nextExercise() {
    if (state.currentWorkout != null && 
        state.currentExerciseIndex < state.currentWorkout!.exercises.length - 1) {
      state = state.copyWith(
        currentExerciseIndex: state.currentExerciseIndex + 1,
        currentSetIndex: 0,
      );
    }
  }

  /// Move to next set
  void nextSet() {
    if (state.currentWorkout != null) {
      final currentExercise = state.currentWorkout!.exercises[state.currentExerciseIndex];
      if (state.currentSetIndex < currentExercise.sets - 1) {
        state = state.copyWith(
          currentSetIndex: state.currentSetIndex + 1,
        );
      }
    }
  }

  /// Update session data
  void updateSessionData(Map<String, dynamic> data) {
    final newSessionData = Map<String, dynamic>.from(state.sessionData);
    newSessionData.addAll(data);
    state = state.copyWith(sessionData: newSessionData);
  }
}

/// Workout session provider
final workoutSessionProvider = StateNotifierProvider<WorkoutSessionNotifier, WorkoutSessionState>((ref) {
  return WorkoutSessionNotifier();
});
