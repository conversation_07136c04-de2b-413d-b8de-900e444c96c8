import '../../../../shared/services/supabase_service.dart';
import '../../domain/models/workout_session.dart';
import '../../../dashboard/domain/models/today_workout.dart';

/// Service for managing workout sessions using your actual database schema
class WorkoutSessionService {
  /// STEP 2: Create workout session entry (100-500ms)
  /// Creates entries in workout_sessions, workout_logs, and checks for incomplete sessions
  Future<String> createWorkoutSession(String workoutPlanId) async {
    final user = SupabaseService.currentUser;
    if (user == null) {
      throw Exception('User not authenticated');
    }

    // TODO: In production, map auth UUID to integer user ID from users table
    // For now, use mock user ID for development
    final userId = 1; // Mock user ID for development

    // Check for incomplete sessions first
    final incompleteSession = await _checkIncompleteSession(userId);
    if (incompleteSession != null) {
      throw WorkoutResumeException(
        'You have an incomplete workout session',
        sessionId: incompleteSession['id'],
        workoutName: incompleteSession['workout_name'],
        currentExercise: incompleteSession['current_exercise_index'],
        currentSet: incompleteSession['current_set'],
      );
    }

    // Create new workout session
    final sessionResponse = await SupabaseService.client
        .from('workout_sessions')
        .insert({
          'user_id': userId,
          'workout_plan_id': workoutPlanId,
          'status': 'in_progress',
          'current_exercise_index': 0,
          'current_set': 1,
          'session_state': {
            'step': 'loading',
            'preloaded_exercises': [],
            'device_settings_applied': false,
          },
        })
        .select('id')
        .single();

    final sessionId = sessionResponse['id'];

    // Create workout log entry
    await SupabaseService.client.from('workout_logs').insert({
      'user_id': userId,
      'workout_session_id': sessionId,
      'workout_plan_id': workoutPlanId,
      'started_at': DateTime.now().toIso8601String(),
    });

    return sessionId;
  }

  /// Check for incomplete workout sessions
  Future<Map<String, dynamic>?> _checkIncompleteSession(int userId) async {
    final response = await SupabaseService.client
        .from('workout_sessions')
        .select('''
          id,
          current_exercise_index,
          current_set,
          workout_plans!inner(name)
        ''')
        .eq('user_id', userId)
        .eq('status', 'in_progress')
        .maybeSingle();

    if (response != null) {
      return {
        'id': response['id'],
        'workout_name': response['workout_plans']['name'],
        'current_exercise_index': response['current_exercise_index'],
        'current_set': response['current_set'],
      };
    }
    return null;
  }

  /// STEP 2: Preload first 3 exercises for smooth experience
  Future<List<Map<String, dynamic>>> preloadExercises(String workoutPlanId) async {
    final exercises = await SupabaseService.client
        .from('workout_exercises')
        .select('''
          id,
          sets,
          reps,
          duration_seconds,
          rest_seconds,
          order_index,
          exercises (
            id,
            name,
            description,
            video_url,
            instructions,
            equipment
          )
        ''')
        .eq('workout_plan_id', workoutPlanId)
        .order('order_index')
        .limit(3);

    return exercises;
  }

  /// Update session state during the loading process
  Future<void> updateSessionState(String sessionId, Map<String, dynamic> state) async {
    await SupabaseService.client
        .from('workout_sessions')
        .update({
          'session_state': state,
          'updated_at': DateTime.now().toIso8601String(),
        })
        .eq('id', sessionId);
  }

  /// STEP 7: Initialize active workout state
  Future<void> initializeActiveState(String sessionId) async {
    await SupabaseService.client
        .from('workout_sessions')
        .update({
          'session_state': {
            'step': 'active',
            'timer_started': true,
            'auto_save_enabled': true,
            'last_save_time': DateTime.now().toIso8601String(),
          },
          'updated_at': DateTime.now().toIso8601String(),
        })
        .eq('id', sessionId);
  }

  /// Auto-save workout progress (every 30 seconds)
  Future<void> saveWorkoutProgress(String sessionId, {
    required int currentExerciseIndex,
    required int currentSet,
    required int totalDurationSeconds,
    Map<String, dynamic>? additionalState,
  }) async {
    final updateData = {
      'current_exercise_index': currentExerciseIndex,
      'current_set': currentSet,
      'total_duration_seconds': totalDurationSeconds,
      'updated_at': DateTime.now().toIso8601String(),
    };

    if (additionalState != null) {
      updateData['session_state'] = additionalState;
    }

    await SupabaseService.client
        .from('workout_sessions')
        .update(updateData)
        .eq('id', sessionId);
  }

  /// Log individual set completion
  Future<void> logSetCompletion(String sessionId, {
    required String workoutExerciseId,
    required String exerciseId,
    required int setNumber,
    required int actualReps,
    required double actualWeightLbs,
    int? actualDurationSeconds,
    int? restDurationSeconds,
    String? notes,
  }) async {
    await SupabaseService.client.from('exercise_sets').insert({
      'workout_session_id': sessionId,
      'workout_exercise_id': workoutExerciseId,
      'exercise_id': exerciseId,
      'set_number': setNumber,
      'actual_reps': actualReps,
      'actual_weight_lbs': actualWeightLbs,
      'actual_duration_seconds': actualDurationSeconds,
      'rest_duration_seconds': restDurationSeconds,
      'notes': notes,
      'completed_at': DateTime.now().toIso8601String(),
    });
  }

  /// Complete workout session
  Future<void> completeWorkout(String sessionId, {
    required int totalSetsCompleted,
    required int totalExercisesCompleted,
    int? caloriesBurned,
    int? rating,
    String? notes,
  }) async {
    final now = DateTime.now().toIso8601String();

    // Update session status
    await SupabaseService.client
        .from('workout_sessions')
        .update({
          'status': 'completed',
          'completed_at': now,
          'updated_at': now,
        })
        .eq('id', sessionId);

    // Update workout log
    await SupabaseService.client
        .from('workout_logs')
        .update({
          'completed_at': now,
          'total_sets_completed': totalSetsCompleted,
          'total_exercises_completed': totalExercisesCompleted,
          'calories_burned': caloriesBurned,
          'rating': rating,
          'notes': notes,
        })
        .eq('workout_session_id', sessionId);
  }

  /// Resume incomplete workout session
  Future<Map<String, dynamic>> resumeWorkoutSession(String sessionId) async {
    final session = await SupabaseService.client
        .from('workout_sessions')
        .select('''
          *,
          workout_plans (
            id,
            name,
            description,
            workout_exercises (
              id,
              sets,
              reps,
              order_index,
              exercises (
                id,
                name,
                description,
                video_url,
                instructions
              )
            )
          )
        ''')
        .eq('id', sessionId)
        .single();

    return session;
  }
}

/// Exception thrown when a workout can be resumed
class WorkoutResumeException implements Exception {
  final String message;
  final String sessionId;
  final String workoutName;
  final int currentExercise;
  final int currentSet;

  WorkoutResumeException(
    this.message, {
    required this.sessionId,
    required this.workoutName,
    required this.currentExercise,
    required this.currentSet,
  });

  @override
  String toString() => message;
}
