import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/spacing.dart';
import '../../../../core/theme/color_palette.dart';

class WorkoutListScreen extends ConsumerWidget {
  const WorkoutListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Workouts'),
      ),
      body: ListView.builder(
        padding: AppSpacing.paddingMd,
        itemCount: 5, // Placeholder count
        itemBuilder: (context, index) {
          return Card(
            margin: AppSpacing.paddingVerticalSm,
            child: ListTile(
              contentPadding: AppSpacing.paddingMd,
              leading: Container(
                padding: AppSpacing.paddingMd,
                decoration: BoxDecoration(
                  color: _getWorkoutTypeColor(index).withOpacity(0.1),
                  borderRadius: AppSpacing.borderRadiusMd,
                ),
                child: Icon(
                  _getWorkoutIcon(index),
                  size: AppSpacing.iconLg,
                  color: _getWorkoutTypeColor(index),
                ),
              ),
              title: Text(
                'Workout ${index + 1}',
                style: textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              subtitle: Text(
                '${_getWorkoutDuration(index)} minutes • ${_getWorkoutType(index)}',
                style: textTheme.bodyMedium,
              ),
              trailing: Icon(
                Icons.chevron_right,
                color: colorScheme.onSurfaceVariant,
              ),
              onTap: () {
                // TODO: Navigate to workout details
              },
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // TODO: Add new workout
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Color _getWorkoutTypeColor(int index) {
    final colors = [
      AppColorPalette.cardio,
      AppColorPalette.strength,
      AppColorPalette.flexibility,
      AppColorPalette.endurance,
      AppColorPalette.primaryBlue,
    ];
    return colors[index % colors.length];
  }

  IconData _getWorkoutIcon(int index) {
    final icons = [
      Icons.directions_run,
      Icons.fitness_center,
      Icons.self_improvement,
      Icons.timer,
      Icons.sports_gymnastics,
    ];
    return icons[index % icons.length];
  }

  String _getWorkoutType(int index) {
    final types = [
      'Cardio',
      'Strength Training',
      'Flexibility',
      'Endurance',
      'Mixed',
    ];
    return types[index % types.length];
  }

  int _getWorkoutDuration(int index) {
    final durations = [25, 30, 45, 20, 35];
    return durations[index % durations.length];
  }
}
