import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/typography.dart';
import '../../../../core/animations/animation_utils.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../domain/models/workout_session.dart';
import '../widgets/exercise_list_item.dart';

class WorkoutDetailsScreen extends ConsumerStatefulWidget {
  final WorkoutSession workout;

  const WorkoutDetailsScreen({
    super.key,
    required this.workout,
  });

  @override
  ConsumerState<WorkoutDetailsScreen> createState() => _WorkoutDetailsScreenState();
}

class _WorkoutDetailsScreenState extends ConsumerState<WorkoutDetailsScreen>
    with TickerProviderStateMixin {
  late AnimationController _headerController;
  late AnimationController _listController;
  late Animation<double> _headerAnimation;
  late List<Animation<double>> _listAnimations;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimations();
  }

  void _setupAnimations() {
    _headerController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _listController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _headerAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _headerController,
        curve: Curves.easeOut,
      ),
    );

    _listAnimations = AnimationUtils.createStaggeredAnimations(
      controller: _listController,
      count: widget.workout.exercises.length,
      staggerDelay: const Duration(milliseconds: 100),
    );
  }

  void _startAnimations() {
    _headerController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      _listController.forward();
    });
  }

  @override
  void dispose() {
    _headerController.dispose();
    _listController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: Stack(
        children: [
          // Background
          _buildBackground(size),
          
          // Content
          CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              _buildSliverAppBar(context, theme),
              _buildMotivationalSection(theme),
              _buildExercisesList(theme),
              _buildBottomPadding(),
            ],
          ),
          
          // Sticky start button
          _buildStickyStartButton(context, theme),
        ],
      ),
    );
  }

  Widget _buildBackground(Size size) {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColorPalette.darkBackground,
              AppColorPalette.elevation1,
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSliverAppBar(BuildContext context, ThemeData theme) {
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: Container(
        margin: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.3),
          borderRadius: BorderRadius.circular(12),
        ),
        child: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.arrow_back, color: Colors.white),
        ),
      ),
      actions: [
        Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.3),
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            onPressed: () => _showWorkoutOptions(context),
            icon: const Icon(Icons.more_vert, color: Colors.white),
          ),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: AnimatedBuilder(
          animation: _headerController,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, 20 * (1 - _headerAnimation.value)),
              child: Opacity(
                opacity: _headerAnimation.value,
                child: _buildHeaderContent(theme),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildHeaderContent(ThemeData theme) {
    return Stack(
      children: [
        // Background image with blur
        if (widget.workout.backgroundImageUrl != null)
          Positioned.fill(
            child: Image.network(
              widget.workout.backgroundImageUrl!,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => _buildDefaultHeaderBackground(),
            ),
          )
        else
          _buildDefaultHeaderBackground(),
        
        // Dark overlay
        Positioned.fill(
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withOpacity(0.3),
                  Colors.black.withOpacity(0.7),
                ],
              ),
            ),
          ),
        ),
        
        // Content
        Positioned(
          bottom: 20,
          left: 20,
          right: 20,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.workout.name,
                style: AppTypography.displayNumbers(
                  fontSize: 28,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '${widget.workout.exercises.length} exercises • ${widget.workout.durationText} • ${widget.workout.caloriesText}',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: Colors.white.withOpacity(0.9),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDefaultHeaderBackground() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColorPalette.primaryOrange,
            AppColorPalette.primaryOrangeLight,
          ],
        ),
      ),
    );
  }

  Widget _buildMotivationalSection(ThemeData theme) {
    return SliverToBoxAdapter(
      child: AnimatedBuilder(
        animation: _headerController,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(0, 30 * (1 - _headerAnimation.value)),
            child: Opacity(
              opacity: _headerAnimation.value,
              child: Container(
                margin: const EdgeInsets.all(20),
                child: GlassMorphismCard(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.psychology_outlined,
                            color: AppColorPalette.primaryOrange,
                            size: 24,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Get Ready',
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(
                        widget.workout.description.isNotEmpty
                            ? widget.workout.description
                            : 'Focus on proper form and controlled movements. Take your time with each exercise and listen to your body.',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.8),
                          height: 1.5,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildExercisesList(ThemeData theme) {
    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      sliver: SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            final exercise = widget.workout.exercises[index];
            final animationIndex = index.clamp(0, _listAnimations.length - 1);
            
            return AnimatedBuilder(
              animation: _listAnimations[animationIndex],
              builder: (context, child) {
                return Transform.scale(
                  scale: _listAnimations[animationIndex].value,
                  child: Opacity(
                    opacity: _listAnimations[animationIndex].value,
                    child: Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      child: ExerciseListItem(
                        exercise: exercise,
                        exerciseNumber: index + 1,
                        onTap: () => _showExerciseDetails(context, exercise),
                      ),
                    ),
                  ),
                );
              },
            );
          },
          childCount: widget.workout.exercises.length,
        ),
      ),
    );
  }

  Widget _buildBottomPadding() {
    return const SliverToBoxAdapter(
      child: SizedBox(height: 100), // Space for sticky button
    );
  }

  Widget _buildStickyStartButton(BuildContext context, ThemeData theme) {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              theme.colorScheme.background.withOpacity(0.0),
              theme.colorScheme.background.withOpacity(0.8),
              theme.colorScheme.background,
            ],
          ),
        ),
        child: SafeArea(
          child: SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => _startWorkout(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColorPalette.primaryOrange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                elevation: 8,
                shadowColor: AppColorPalette.primaryOrange.withOpacity(0.5),
              ),
              child: Text(
                'Start Workout',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showWorkoutOptions(BuildContext context) {
    HapticFeedback.lightImpact();
    // TODO: Show workout options bottom sheet
  }

  void _showExerciseDetails(BuildContext context, WorkoutExercise exercise) {
    HapticFeedback.lightImpact();
    // TODO: Show exercise details modal
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildExerciseDetailsModal(context, exercise),
    );
  }

  Widget _buildExerciseDetailsModal(BuildContext context, WorkoutExercise exercise) {
    final theme = Theme.of(context);
    
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: theme.colorScheme.onSurface.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    exercise.name,
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${exercise.sets} sets • ${exercise.targetReps.first} reps • ${exercise.currentWeightText}',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                  const SizedBox(height: 20),
                  if (exercise.instructions?.isNotEmpty == true) ...[
                    Text(
                      'Instructions',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      exercise.instructions!,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        height: 1.5,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _startWorkout(BuildContext context) {
    HapticFeedback.mediumImpact();
    // TODO: Navigate to active workout screen
    Navigator.pushNamed(context, '/active-workout', arguments: widget.workout);
  }
}
