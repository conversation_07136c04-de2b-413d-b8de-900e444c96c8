import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/spacing.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../../dashboard/domain/providers/dashboard_provider.dart';
import '../../domain/providers/workout_session_provider.dart';
import '../../data/services/workout_session_service.dart';

/// Loading screen shown during workout preparation
class WorkoutLoadingScreen extends ConsumerStatefulWidget {
  final String? workoutId;

  const WorkoutLoadingScreen({
    super.key,
    this.workoutId,
  });

  @override
  ConsumerState<WorkoutLoadingScreen> createState() => _WorkoutLoadingScreenState();
}

class _WorkoutLoadingScreenState extends ConsumerState<WorkoutLoadingScreen>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _pulseController;
  late Animation<double> _progressAnimation;
  late Animation<double> _pulseAnimation;

  final List<LoadingStep> _loadingSteps = [
    LoadingStep('Loading exercises', Duration(milliseconds: 400)),
    LoadingStep('Downloading videos', Duration(milliseconds: 1100)),
    LoadingStep('Setting up timer', Duration(milliseconds: 500)),
  ];

  int _currentStepIndex = 0;
  bool _isComplete = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startLoadingProcess();
  }

  void _setupAnimations() {
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _pulseController.repeat(reverse: true);
  }

  void _startLoadingProcess() async {
    // Get today's workout
    final dashboardData = await ref.read(dashboardDataProvider.future);
    final todayWorkout = dashboardData.todayWorkout;

    if (todayWorkout == null) {
      _showError('No workout found for today');
      return;
    }

    // STEP 2: Pre-flight checks and database operations (100-500ms)
    try {
      final workoutSessionService = WorkoutSessionService();
      String? sessionId;

      // Animate through loading steps
      _progressController.forward();

      // Step 1: Loading exercises (0-400ms)
      setState(() {
        _currentStepIndex = 0;
      });

      try {
        // Create workout session and check for incomplete sessions
        sessionId = await workoutSessionService.createWorkoutSession(todayWorkout.id);

        // Preload first 3 exercises
        final preloadedExercises = await workoutSessionService.preloadExercises(todayWorkout.id);

        await Future.delayed(const Duration(milliseconds: 400));

      } on WorkoutResumeException catch (resumeException) {
        // Handle resume scenario
        final shouldResume = await _showResumeDialog(resumeException);
        if (shouldResume) {
          sessionId = resumeException.sessionId;
        } else {
          // User chose to start over, create new session
          sessionId = await workoutSessionService.createWorkoutSession(todayWorkout.id);
        }
      }

      // Step 2: Downloading videos (400-1500ms)
      setState(() {
        _currentStepIndex = 1;
      });

      // Background tasks: preload videos, check storage, initialize tracking
      await Future.wait([
        _preloadVideos(todayWorkout.exercises.take(3).toList()),
        _checkDeviceStorage(),
        _initializeHealthTracking(),
        _configureDeviceSettings(),
      ]);

      await Future.delayed(const Duration(milliseconds: 700)); // Remaining time

      // Step 3: Setting up timer (1500-2000ms)
      setState(() {
        _currentStepIndex = 2;
      });

      // Initialize active state
      if (sessionId != null) {
        await workoutSessionService.initializeActiveState(sessionId);

        // Update session state
        await workoutSessionService.updateSessionState(sessionId, {
          'step': 'ready_for_countdown',
          'preloaded_exercises': 3,
          'device_settings_applied': true,
          'session_ready': true,
        });
      }

      await Future.delayed(const Duration(milliseconds: 500));

      setState(() {
        _isComplete = true;
      });

      // Navigate to countdown screen after a brief delay
      await Future.delayed(const Duration(milliseconds: 500));

      if (mounted) {
        context.go('/workout-countdown', extra: {
          'workout': todayWorkout,
          'sessionId': sessionId,
        });
      }

    } catch (e) {
      _showError(e.toString());
    }
  }

  /// Show resume dialog when incomplete session is found
  Future<bool> _showResumeDialog(WorkoutResumeException resumeException) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: AppColorPalette.darkBackground,
        title: Text(
          'Resume Workout?',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'You have an incomplete workout:',
              style: TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 8),
            Text(
              resumeException.workoutName,
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              'Exercise ${resumeException.currentExercise + 1} - Set ${resumeException.currentSet}',
              style: TextStyle(color: AppColorPalette.primaryOrange),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('Start Over', style: TextStyle(color: Colors.white70)),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColorPalette.primaryOrange,
            ),
            child: Text('Resume'),
          ),
        ],
      ),
    ) ?? false;
  }

  /// Background task: Preload exercise videos
  Future<void> _preloadVideos(List<dynamic> exercises) async {
    // Simulate video preloading
    await Future.delayed(const Duration(milliseconds: 300));
    print('📹 Preloaded ${exercises.length} exercise videos');
  }

  /// Background task: Check device storage
  Future<void> _checkDeviceStorage() async {
    await Future.delayed(const Duration(milliseconds: 100));
    print('💾 Device storage check complete');
  }

  /// Background task: Initialize health tracking
  Future<void> _initializeHealthTracking() async {
    await Future.delayed(const Duration(milliseconds: 200));
    print('❤️ Health tracking initialized');
  }

  /// Background task: Configure device settings
  Future<void> _configureDeviceSettings() async {
    await Future.delayed(const Duration(milliseconds: 150));
    // Set screen brightness to 80%, disable auto-lock
    print('⚙️ Device settings configured');
  }

  void _showError(String message) {
    setState(() {
      _isComplete = true;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColorPalette.error,
        action: SnackBarAction(
          label: 'Retry',
          textColor: Colors.white,
          onPressed: () {
            setState(() {
              _currentStepIndex = 0;
              _isComplete = false;
            });
            _progressController.reset();
            _startLoadingProcess();
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    _progressController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: AppColorPalette.darkBackground,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppSpacing.lg),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Logo with pulse animation
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        gradient: AppColorPalette.primaryGradient,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppColorPalette.primaryOrange.withOpacity(0.3),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.fitness_center,
                        color: Colors.white,
                        size: 60,
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: AppSpacing.xxl),

              // OpenFit title
              Text(
                'OpenFit',
                style: theme.textTheme.headlineLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 1.5,
                ),
              ),

              const SizedBox(height: AppSpacing.xl),

              // Progress bar
              GlassMorphismCard(
                padding: const EdgeInsets.all(AppSpacing.md),
                child: Column(
                  children: [
                    AnimatedBuilder(
                      animation: _progressAnimation,
                      builder: (context, child) {
                        return LinearProgressIndicator(
                          value: _progressAnimation.value,
                          backgroundColor: Colors.white.withOpacity(0.1),
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppColorPalette.primaryOrange,
                          ),
                          minHeight: 8,
                        );
                      },
                    ),
                    const SizedBox(height: AppSpacing.md),
                    Text(
                      'Preparing your workout',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppSpacing.xl),

              // Loading steps
              Column(
                children: _loadingSteps.asMap().entries.map((entry) {
                  final index = entry.key;
                  final step = entry.value;
                  final isActive = index == _currentStepIndex && !_isComplete;
                  final isCompleted = index < _currentStepIndex || _isComplete;

                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: AppSpacing.sm),
                    child: Row(
                      children: [
                        // Status icon
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: isCompleted 
                                ? AppColorPalette.successGreen
                                : isActive 
                                    ? AppColorPalette.primaryOrange
                                    : Colors.white.withOpacity(0.3),
                            shape: BoxShape.circle,
                          ),
                          child: isCompleted
                              ? const Icon(
                                  Icons.check,
                                  color: Colors.white,
                                  size: 16,
                                )
                              : isActive
                                  ? SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(
                                          Colors.white,
                                        ),
                                      ),
                                    )
                                  : null,
                        ),

                        const SizedBox(width: AppSpacing.md),

                        // Step text
                        Text(
                          step.title,
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: isCompleted || isActive
                                ? Colors.white
                                : Colors.white.withOpacity(0.6),
                            fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),

              const SizedBox(height: AppSpacing.xxl),

              // Cancel button
              TextButton(
                onPressed: () {
                  context.go('/');
                },
                child: Text(
                  'Cancel',
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.7),
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Loading step model
class LoadingStep {
  final String title;
  final Duration duration;

  const LoadingStep(this.title, this.duration);
}
