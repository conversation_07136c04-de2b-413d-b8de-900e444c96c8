import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/typography.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';

class SetCounterWidget extends StatefulWidget {
  final int currentReps;
  final double currentWeight;
  final ValueChanged<int>? onRepsChanged;
  final ValueChanged<double>? onWeightChanged;
  final bool isImperial;

  const SetCounterWidget({
    super.key,
    required this.currentReps,
    required this.currentWeight,
    this.onRepsChanged,
    this.onWeightChanged,
    this.isImperial = true,
  });

  @override
  State<SetCounterWidget> createState() => _SetCounterWidgetState();
}

class _SetCounterWidgetState extends State<SetCounterWidget>
    with TickerProviderStateMixin {
  late AnimationController _repsController;
  late AnimationController _weightController;
  late Animation<double> _repsScaleAnimation;
  late Animation<double> _weightScaleAnimation;
  
  late int _reps;
  late double _weight;

  @override
  void initState() {
    super.initState();
    _reps = widget.currentReps;
    _weight = widget.currentWeight;
    
    _setupAnimations();
  }

  void _setupAnimations() {
    _repsController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _weightController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _repsScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(
      CurvedAnimation(
        parent: _repsController,
        curve: Curves.elasticOut,
      ),
    );

    _weightScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(
      CurvedAnimation(
        parent: _weightController,
        curve: Curves.elasticOut,
      ),
    );
  }

  @override
  void dispose() {
    _repsController.dispose();
    _weightController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      children: [
        // Reps counter
        Expanded(
          child: _buildCounter(
            title: 'Reps',
            value: _reps.toString(),
            unit: _reps == 1 ? 'rep' : 'reps',
            onIncrement: _incrementReps,
            onDecrement: _decrementReps,
            onTap: _editReps,
            animation: _repsScaleAnimation,
            theme: theme,
          ),
        ),
        
        const SizedBox(width: 20),
        
        // Weight counter
        Expanded(
          child: _buildCounter(
            title: 'Weight',
            value: _weight == 0 ? 'BW' : _weight.toInt().toString(),
            unit: _weight == 0 ? '' : (widget.isImperial ? 'lbs' : 'kg'),
            onIncrement: _incrementWeight,
            onDecrement: _decrementWeight,
            onTap: _editWeight,
            animation: _weightScaleAnimation,
            theme: theme,
            canDecrement: _weight > 0,
          ),
        ),
      ],
    );
  }

  Widget _buildCounter({
    required String title,
    required String value,
    required String unit,
    required VoidCallback onIncrement,
    required VoidCallback onDecrement,
    required VoidCallback onTap,
    required Animation<double> animation,
    required ThemeData theme,
    bool canDecrement = true,
  }) {
    return GlassMorphismCard(
      child: Column(
        children: [
          // Title
          Text(
            title,
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Counter controls
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Decrement button
              _buildCounterButton(
                icon: Icons.remove,
                onPressed: canDecrement ? onDecrement : null,
                theme: theme,
              ),
              
              // Value display
              Expanded(
                child: GestureDetector(
                  onTap: onTap,
                  child: AnimatedBuilder(
                    animation: animation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: animation.value,
                        child: Column(
                          children: [
                            Text(
                              value,
                              style: AppTypography.displayNumbers(
                                fontSize: 32,
                                color: Colors.white,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            if (unit.isNotEmpty)
                              Text(
                                unit,
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.7),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),
              
              // Increment button
              _buildCounterButton(
                icon: Icons.add,
                onPressed: onIncrement,
                theme: theme,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCounterButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required ThemeData theme,
  }) {
    return Container(
      width: 44,
      height: 44,
      decoration: BoxDecoration(
        color: onPressed != null 
            ? AppColorPalette.primaryOrange.withOpacity(0.2)
            : Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(22),
        border: Border.all(
          color: onPressed != null 
              ? AppColorPalette.primaryOrange.withOpacity(0.5)
              : Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          color: onPressed != null 
              ? AppColorPalette.primaryOrange
              : Colors.white.withOpacity(0.3),
          size: 20,
        ),
        padding: EdgeInsets.zero,
      ),
    );
  }

  void _incrementReps() {
    HapticFeedback.lightImpact();
    setState(() {
      _reps++;
    });
    _repsController.forward().then((_) => _repsController.reverse());
    widget.onRepsChanged?.call(_reps);
  }

  void _decrementReps() {
    if (_reps > 1) {
      HapticFeedback.lightImpact();
      setState(() {
        _reps--;
      });
      _repsController.forward().then((_) => _repsController.reverse());
      widget.onRepsChanged?.call(_reps);
    }
  }

  void _incrementWeight() {
    HapticFeedback.lightImpact();
    setState(() {
      if (_weight == 0) {
        _weight = widget.isImperial ? 5 : 2.5; // Start with 5 lbs or 2.5 kg
      } else {
        _weight += widget.isImperial ? 5 : 2.5; // Increment by 5 lbs or 2.5 kg
      }
    });
    _weightController.forward().then((_) => _weightController.reverse());
    widget.onWeightChanged?.call(_weight);
  }

  void _decrementWeight() {
    if (_weight > 0) {
      HapticFeedback.lightImpact();
      setState(() {
        _weight -= widget.isImperial ? 5 : 2.5;
        if (_weight < 0) _weight = 0;
      });
      _weightController.forward().then((_) => _weightController.reverse());
      widget.onWeightChanged?.call(_weight);
    }
  }

  void _editReps() {
    HapticFeedback.lightImpact();
    _showNumberPicker(
      title: 'Edit Reps',
      currentValue: _reps,
      minValue: 1,
      maxValue: 100,
      onChanged: (value) {
        setState(() {
          _reps = value;
        });
        widget.onRepsChanged?.call(_reps);
      },
    );
  }

  void _editWeight() {
    HapticFeedback.lightImpact();
    _showNumberPicker(
      title: 'Edit Weight',
      currentValue: _weight.toInt(),
      minValue: 0,
      maxValue: widget.isImperial ? 1000 : 500,
      step: widget.isImperial ? 5 : 2.5,
      unit: _weight == 0 ? 'Bodyweight' : (widget.isImperial ? 'lbs' : 'kg'),
      onChanged: (value) {
        setState(() {
          _weight = value.toDouble();
        });
        widget.onWeightChanged?.call(_weight);
      },
    );
  }

  void _showNumberPicker({
    required String title,
    required int currentValue,
    required int minValue,
    required int maxValue,
    double step = 1,
    String? unit,
    required ValueChanged<int> onChanged,
  }) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => NumberPickerModal(
        title: title,
        currentValue: currentValue,
        minValue: minValue,
        maxValue: maxValue,
        step: step,
        unit: unit,
        onChanged: onChanged,
      ),
    );
  }
}

class NumberPickerModal extends StatefulWidget {
  final String title;
  final int currentValue;
  final int minValue;
  final int maxValue;
  final double step;
  final String? unit;
  final ValueChanged<int> onChanged;

  const NumberPickerModal({
    super.key,
    required this.title,
    required this.currentValue,
    required this.minValue,
    required this.maxValue,
    this.step = 1,
    this.unit,
    required this.onChanged,
  });

  @override
  State<NumberPickerModal> createState() => _NumberPickerModalState();
}

class _NumberPickerModalState extends State<NumberPickerModal> {
  late int _selectedValue;

  @override
  void initState() {
    super.initState();
    _selectedValue = widget.currentValue;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  widget.title,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),
          
          // Number picker
          SizedBox(
            height: 200,
            child: ListWheelScrollView.useDelegate(
              itemExtent: 50,
              perspective: 0.005,
              diameterRatio: 1.2,
              physics: const FixedExtentScrollPhysics(),
              onSelectedItemChanged: (index) {
                setState(() {
                  _selectedValue = widget.minValue + (index * widget.step).round();
                });
              },
              childDelegate: ListWheelChildBuilderDelegate(
                childCount: ((widget.maxValue - widget.minValue) / widget.step).round() + 1,
                builder: (context, index) {
                  final value = widget.minValue + (index * widget.step).round();
                  final isSelected = value == _selectedValue;
                  
                  return Center(
                    child: Text(
                      value == 0 && widget.unit == 'Bodyweight' 
                          ? 'BW'
                          : '$value${widget.unit != null ? ' ${widget.unit}' : ''}',
                      style: TextStyle(
                        fontSize: isSelected ? 24 : 18,
                        fontWeight: isSelected ? FontWeight.w700 : FontWeight.w500,
                        color: isSelected 
                            ? AppColorPalette.primaryOrange
                            : theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          
          // Confirm button
          Padding(
            padding: const EdgeInsets.all(20),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  widget.onChanged(_selectedValue);
                  Navigator.of(context).pop();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColorPalette.primaryOrange,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Confirm',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
