import 'package:flutter/material.dart';
import '../../../../core/theme/typography.dart';

class WorkoutTimer extends StatelessWidget {
  final Duration elapsedTime;
  final Color? textColor;
  final double fontSize;
  final bool showMilliseconds;

  const WorkoutTimer({
    super.key,
    required this.elapsedTime,
    this.textColor,
    this.fontSize = 20,
    this.showMilliseconds = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveTextColor = textColor ?? theme.colorScheme.onSurface;

    return Text(
      _formatDuration(elapsedTime),
      style: AppTypography.displayNumbers(
        fontSize: fontSize,
        color: effectiveTextColor,
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    final milliseconds = duration.inMilliseconds.remainder(1000);

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:'
             '${minutes.toString().padLeft(2, '0')}:'
             '${seconds.toString().padLeft(2, '0')}';
    } else if (showMilliseconds) {
      return '${minutes.toString().padLeft(2, '0')}:'
             '${seconds.toString().padLeft(2, '0')}.'
             '${(milliseconds ~/ 10).toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:'
             '${seconds.toString().padLeft(2, '0')}';
    }
  }
}

/// Countdown timer widget for rest periods
class CountdownTimer extends StatefulWidget {
  final Duration duration;
  final VoidCallback? onComplete;
  final Color? textColor;
  final Color? progressColor;
  final double size;

  const CountdownTimer({
    super.key,
    required this.duration,
    this.onComplete,
    this.textColor,
    this.progressColor,
    this.size = 120,
  });

  @override
  State<CountdownTimer> createState() => _CountdownTimerState();
}

class _CountdownTimerState extends State<CountdownTimer>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  Duration _remainingTime = Duration.zero;

  @override
  void initState() {
    super.initState();
    _remainingTime = widget.duration;
    
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.linear,
      ),
    );

    _controller.addListener(() {
      setState(() {
        _remainingTime = Duration(
          milliseconds: (widget.duration.inMilliseconds * _animation.value).round(),
        );
      });
    });

    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        widget.onComplete?.call();
      }
    });

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveTextColor = widget.textColor ?? theme.colorScheme.onSurface;
    final effectiveProgressColor = widget.progressColor ?? theme.colorScheme.primary;

    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Background circle
          SizedBox(
            width: widget.size,
            height: widget.size,
            child: CircularProgressIndicator(
              value: 1.0,
              strokeWidth: 8,
              backgroundColor: effectiveProgressColor.withOpacity(0.2),
              valueColor: AlwaysStoppedAnimation<Color>(Colors.transparent),
            ),
          ),
          
          // Progress circle
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return SizedBox(
                width: widget.size,
                height: widget.size,
                child: CircularProgressIndicator(
                  value: _animation.value,
                  strokeWidth: 8,
                  backgroundColor: Colors.transparent,
                  valueColor: AlwaysStoppedAnimation<Color>(effectiveProgressColor),
                ),
              );
            },
          ),
          
          // Time text
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                _formatCountdown(_remainingTime),
                style: AppTypography.displayNumbers(
                  fontSize: widget.size * 0.2,
                  color: effectiveTextColor,
                ),
              ),
              Text(
                'remaining',
                style: TextStyle(
                  fontSize: widget.size * 0.08,
                  color: effectiveTextColor.withOpacity(0.7),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatCountdown(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds.remainder(60);
    
    if (minutes > 0) {
      return '${minutes}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return seconds.toString();
    }
  }

  void pause() {
    _controller.stop();
  }

  void resume() {
    _controller.forward();
  }

  void reset() {
    _controller.reset();
    setState(() {
      _remainingTime = widget.duration;
    });
  }

  void addTime(Duration additionalTime) {
    final newDuration = _remainingTime + additionalTime;
    final progress = 1.0 - (newDuration.inMilliseconds / widget.duration.inMilliseconds);
    
    _controller.reset();
    _controller.animateTo(progress.clamp(0.0, 1.0));
  }

  void subtractTime(Duration timeToSubtract) {
    final newDuration = _remainingTime - timeToSubtract;
    if (newDuration.inMilliseconds <= 0) {
      _controller.forward();
      return;
    }
    
    final progress = 1.0 - (newDuration.inMilliseconds / widget.duration.inMilliseconds);
    _controller.animateTo(progress.clamp(0.0, 1.0));
  }
}

/// Simple stopwatch widget
class StopwatchTimer extends StatefulWidget {
  final Color? textColor;
  final double fontSize;
  final bool autoStart;

  const StopwatchTimer({
    super.key,
    this.textColor,
    this.fontSize = 24,
    this.autoStart = true,
  });

  @override
  State<StopwatchTimer> createState() => _StopwatchTimerState();
}

class _StopwatchTimerState extends State<StopwatchTimer> {
  late Stopwatch _stopwatch;
  Duration _elapsed = Duration.zero;

  @override
  void initState() {
    super.initState();
    _stopwatch = Stopwatch();
    
    if (widget.autoStart) {
      start();
    }
    
    // Update every 100ms for smooth display
    Stream.periodic(const Duration(milliseconds: 100)).listen((_) {
      if (mounted && _stopwatch.isRunning) {
        setState(() {
          _elapsed = _stopwatch.elapsed;
        });
      }
    });
  }

  @override
  void dispose() {
    _stopwatch.stop();
    super.dispose();
  }

  void start() {
    _stopwatch.start();
  }

  void stop() {
    _stopwatch.stop();
  }

  void reset() {
    _stopwatch.reset();
    setState(() {
      _elapsed = Duration.zero;
    });
  }

  void restart() {
    _stopwatch.reset();
    _stopwatch.start();
    setState(() {
      _elapsed = Duration.zero;
    });
  }

  @override
  Widget build(BuildContext context) {
    return WorkoutTimer(
      elapsedTime: _elapsed,
      textColor: widget.textColor,
      fontSize: widget.fontSize,
      showMilliseconds: true,
    );
  }
}
