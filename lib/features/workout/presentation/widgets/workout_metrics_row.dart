import 'package:flutter/material.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/typography.dart';

class WorkoutMetricsRow extends StatelessWidget {
  final int duration; // in minutes
  final int calories;
  final int exercises;
  final int sets;
  final Color? textColor;
  final Color? iconColor;
  final bool showBackground;

  const WorkoutMetricsRow({
    super.key,
    required this.duration,
    required this.calories,
    required this.exercises,
    required this.sets,
    this.textColor,
    this.iconColor,
    this.showBackground = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveTextColor = textColor ?? theme.colorScheme.onSurface;
    final effectiveIconColor = iconColor ?? AppColorPalette.primaryOrange;

    return Container(
      padding: showBackground 
          ? const EdgeInsets.symmetric(horizontal: 20, vertical: 16)
          : EdgeInsets.zero,
      decoration: showBackground
          ? BoxDecoration(
              color: theme.colorScheme.surface.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: effectiveTextColor.withOpacity(0.1),
                width: 1,
              ),
            )
          : null,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildMetric(
            icon: Icons.timer_outlined,
            value: duration.toString(),
            unit: 'min',
            label: 'Duration',
            textColor: effectiveTextColor,
            iconColor: effectiveIconColor,
          ),
          _buildDivider(effectiveTextColor),
          _buildMetric(
            icon: Icons.local_fire_department_outlined,
            value: calories.toString(),
            unit: 'kcal',
            label: 'Calories',
            textColor: effectiveTextColor,
            iconColor: effectiveIconColor,
          ),
          _buildDivider(effectiveTextColor),
          _buildMetric(
            icon: Icons.fitness_center_outlined,
            value: exercises.toString(),
            unit: exercises == 1 ? 'exercise' : 'exercises',
            label: 'Exercises',
            textColor: effectiveTextColor,
            iconColor: effectiveIconColor,
          ),
          _buildDivider(effectiveTextColor),
          _buildMetric(
            icon: Icons.repeat_outlined,
            value: sets.toString(),
            unit: sets == 1 ? 'set' : 'sets',
            label: 'Sets',
            textColor: effectiveTextColor,
            iconColor: effectiveIconColor,
          ),
        ],
      ),
    );
  }

  Widget _buildMetric({
    required IconData icon,
    required String value,
    required String unit,
    required String label,
    required Color textColor,
    required Color iconColor,
  }) {
    return Expanded(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: iconColor,
            size: 20,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: AppTypography.displayNumbers(
              fontSize: 18,
              color: textColor,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            unit,
            style: TextStyle(
              fontSize: 12,
              color: textColor.withOpacity(0.8),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider(Color color) {
    return Container(
      width: 1,
      height: 40,
      color: color.withOpacity(0.2),
      margin: const EdgeInsets.symmetric(horizontal: 8),
    );
  }
}

/// Compact version of workout metrics for smaller spaces
class CompactWorkoutMetrics extends StatelessWidget {
  final int duration;
  final int calories;
  final int sets;
  final Color? textColor;
  final Color? iconColor;

  const CompactWorkoutMetrics({
    super.key,
    required this.duration,
    required this.calories,
    required this.sets,
    this.textColor,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveTextColor = textColor ?? theme.colorScheme.onSurface;
    final effectiveIconColor = iconColor ?? AppColorPalette.primaryOrange;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildCompactMetric(
          icon: Icons.timer_outlined,
          text: '${duration}m',
          textColor: effectiveTextColor,
          iconColor: effectiveIconColor,
        ),
        const SizedBox(width: 16),
        _buildCompactMetric(
          icon: Icons.local_fire_department_outlined,
          text: '${calories} kcal',
          textColor: effectiveTextColor,
          iconColor: effectiveIconColor,
        ),
        const SizedBox(width: 16),
        _buildCompactMetric(
          icon: Icons.repeat_outlined,
          text: '$sets ${sets == 1 ? 'set' : 'sets'}',
          textColor: effectiveTextColor,
          iconColor: effectiveIconColor,
        ),
      ],
    );
  }

  Widget _buildCompactMetric({
    required IconData icon,
    required String text,
    required Color textColor,
    required Color iconColor,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          color: iconColor,
          size: 16,
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(
            fontSize: 14,
            color: textColor,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}

/// Animated workout metrics that count up
class AnimatedWorkoutMetrics extends StatefulWidget {
  final int duration;
  final int calories;
  final int exercises;
  final int sets;
  final Color? textColor;
  final Color? iconColor;
  final Duration animationDuration;

  const AnimatedWorkoutMetrics({
    super.key,
    required this.duration,
    required this.calories,
    required this.exercises,
    required this.sets,
    this.textColor,
    this.iconColor,
    this.animationDuration = const Duration(milliseconds: 1500),
  });

  @override
  State<AnimatedWorkoutMetrics> createState() => _AnimatedWorkoutMetricsState();
}

class _AnimatedWorkoutMetricsState extends State<AnimatedWorkoutMetrics>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimations();
  }

  void _setupAnimations() {
    _controllers = List.generate(4, (index) {
      return AnimationController(
        duration: widget.animationDuration,
        vsync: this,
      );
    });

    _animations = [
      Tween<double>(begin: 0, end: widget.duration.toDouble()).animate(
        CurvedAnimation(parent: _controllers[0], curve: Curves.easeOut),
      ),
      Tween<double>(begin: 0, end: widget.calories.toDouble()).animate(
        CurvedAnimation(parent: _controllers[1], curve: Curves.easeOut),
      ),
      Tween<double>(begin: 0, end: widget.exercises.toDouble()).animate(
        CurvedAnimation(parent: _controllers[2], curve: Curves.easeOut),
      ),
      Tween<double>(begin: 0, end: widget.sets.toDouble()).animate(
        CurvedAnimation(parent: _controllers[3], curve: Curves.easeOut),
      ),
    ];
  }

  void _startAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 200), () {
        if (mounted) {
          _controllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge(_controllers),
      builder: (context, child) {
        return WorkoutMetricsRow(
          duration: _animations[0].value.round(),
          calories: _animations[1].value.round(),
          exercises: _animations[2].value.round(),
          sets: _animations[3].value.round(),
          textColor: widget.textColor,
          iconColor: widget.iconColor,
        );
      },
    );
  }
}
