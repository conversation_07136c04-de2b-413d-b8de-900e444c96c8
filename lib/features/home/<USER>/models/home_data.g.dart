// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$HomeDataImpl _$$HomeDataImplFromJson(Map<String, dynamic> json) =>
    _$HomeDataImpl(
      userStats: UserStats.fromJson(json['userStats'] as Map<String, dynamic>),
      recentWorkouts: (json['recentWorkouts'] as List<dynamic>)
          .map((e) => WorkoutSummary.fromJson(e as Map<String, dynamic>))
          .toList(),
      recommendedWorkouts: (json['recommendedWorkouts'] as List<dynamic>)
          .map((e) => WorkoutSummary.fromJson(e as Map<String, dynamic>))
          .toList(),
      todayProgress:
          TodayProgress.fromJson(json['todayProgress'] as Map<String, dynamic>),
      currentStreak: (json['currentStreak'] as num).toInt(),
      totalPoints: (json['totalPoints'] as num).toInt(),
    );

Map<String, dynamic> _$$HomeDataImplToJson(_$HomeDataImpl instance) =>
    <String, dynamic>{
      'userStats': instance.userStats.toJson(),
      'recentWorkouts': instance.recentWorkouts.map((e) => e.toJson()).toList(),
      'recommendedWorkouts':
          instance.recommendedWorkouts.map((e) => e.toJson()).toList(),
      'todayProgress': instance.todayProgress.toJson(),
      'currentStreak': instance.currentStreak,
      'totalPoints': instance.totalPoints,
    };

_$UserStatsImpl _$$UserStatsImplFromJson(Map<String, dynamic> json) =>
    _$UserStatsImpl(
      totalWorkouts: (json['totalWorkouts'] as num).toInt(),
      totalMinutes: (json['totalMinutes'] as num).toInt(),
      totalCalories: (json['totalCalories'] as num).toInt(),
      avgWorkoutDuration: (json['avgWorkoutDuration'] as num).toDouble(),
      workoutsThisWeek: (json['workoutsThisWeek'] as num).toInt(),
      workoutsThisMonth: (json['workoutsThisMonth'] as num).toInt(),
    );

Map<String, dynamic> _$$UserStatsImplToJson(_$UserStatsImpl instance) =>
    <String, dynamic>{
      'totalWorkouts': instance.totalWorkouts,
      'totalMinutes': instance.totalMinutes,
      'totalCalories': instance.totalCalories,
      'avgWorkoutDuration': instance.avgWorkoutDuration,
      'workoutsThisWeek': instance.workoutsThisWeek,
      'workoutsThisMonth': instance.workoutsThisMonth,
    };

_$WorkoutSummaryImpl _$$WorkoutSummaryImplFromJson(Map<String, dynamic> json) =>
    _$WorkoutSummaryImpl(
      id: json['id'] as String,
      name: json['name'] as String,
      category: json['category'] as String,
      duration: (json['duration'] as num).toInt(),
      difficulty: json['difficulty'] as String,
      exercises: (json['exercises'] as num).toInt(),
      calories: (json['calories'] as num).toInt(),
      rating: (json['rating'] as num).toDouble(),
      equipment:
          (json['equipment'] as List<dynamic>).map((e) => e as String).toList(),
      imageUrl: json['imageUrl'] as String?,
      lastCompleted: json['lastCompleted'] == null
          ? null
          : DateTime.parse(json['lastCompleted'] as String),
      isCompleted: json['isCompleted'] as bool? ?? false,
    );

Map<String, dynamic> _$$WorkoutSummaryImplToJson(
        _$WorkoutSummaryImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'category': instance.category,
      'duration': instance.duration,
      'difficulty': instance.difficulty,
      'exercises': instance.exercises,
      'calories': instance.calories,
      'rating': instance.rating,
      'equipment': instance.equipment,
      'imageUrl': instance.imageUrl,
      'lastCompleted': instance.lastCompleted?.toIso8601String(),
      'isCompleted': instance.isCompleted,
    };

_$TodayProgressImpl _$$TodayProgressImplFromJson(Map<String, dynamic> json) =>
    _$TodayProgressImpl(
      calories: (json['calories'] as num).toInt(),
      caloriesGoal: (json['caloriesGoal'] as num).toInt(),
      steps: (json['steps'] as num).toInt(),
      stepsGoal: (json['stepsGoal'] as num).toInt(),
      activeMinutes: (json['activeMinutes'] as num).toInt(),
      activeMinutesGoal: (json['activeMinutesGoal'] as num).toInt(),
      heartRate: (json['heartRate'] as num).toInt(),
      waterIntake: (json['waterIntake'] as num).toInt(),
      waterIntakeGoal: (json['waterIntakeGoal'] as num).toInt(),
    );

Map<String, dynamic> _$$TodayProgressImplToJson(_$TodayProgressImpl instance) =>
    <String, dynamic>{
      'calories': instance.calories,
      'caloriesGoal': instance.caloriesGoal,
      'steps': instance.steps,
      'stepsGoal': instance.stepsGoal,
      'activeMinutes': instance.activeMinutes,
      'activeMinutesGoal': instance.activeMinutesGoal,
      'heartRate': instance.heartRate,
      'waterIntake': instance.waterIntake,
      'waterIntakeGoal': instance.waterIntakeGoal,
    };
