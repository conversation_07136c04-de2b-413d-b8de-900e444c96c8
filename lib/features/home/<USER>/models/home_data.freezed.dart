// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'home_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

HomeData _$HomeDataFromJson(Map<String, dynamic> json) {
  return _HomeData.fromJson(json);
}

/// @nodoc
mixin _$HomeData {
  UserStats get userStats => throw _privateConstructorUsedError;
  List<WorkoutSummary> get recentWorkouts => throw _privateConstructorUsedError;
  List<WorkoutSummary> get recommendedWorkouts =>
      throw _privateConstructorUsedError;
  TodayProgress get todayProgress => throw _privateConstructorUsedError;
  int get currentStreak => throw _privateConstructorUsedError;
  int get totalPoints => throw _privateConstructorUsedError;

  /// Serializes this HomeData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of HomeData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $HomeDataCopyWith<HomeData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HomeDataCopyWith<$Res> {
  factory $HomeDataCopyWith(HomeData value, $Res Function(HomeData) then) =
      _$HomeDataCopyWithImpl<$Res, HomeData>;
  @useResult
  $Res call(
      {UserStats userStats,
      List<WorkoutSummary> recentWorkouts,
      List<WorkoutSummary> recommendedWorkouts,
      TodayProgress todayProgress,
      int currentStreak,
      int totalPoints});

  $UserStatsCopyWith<$Res> get userStats;
  $TodayProgressCopyWith<$Res> get todayProgress;
}

/// @nodoc
class _$HomeDataCopyWithImpl<$Res, $Val extends HomeData>
    implements $HomeDataCopyWith<$Res> {
  _$HomeDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of HomeData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userStats = null,
    Object? recentWorkouts = null,
    Object? recommendedWorkouts = null,
    Object? todayProgress = null,
    Object? currentStreak = null,
    Object? totalPoints = null,
  }) {
    return _then(_value.copyWith(
      userStats: null == userStats
          ? _value.userStats
          : userStats // ignore: cast_nullable_to_non_nullable
              as UserStats,
      recentWorkouts: null == recentWorkouts
          ? _value.recentWorkouts
          : recentWorkouts // ignore: cast_nullable_to_non_nullable
              as List<WorkoutSummary>,
      recommendedWorkouts: null == recommendedWorkouts
          ? _value.recommendedWorkouts
          : recommendedWorkouts // ignore: cast_nullable_to_non_nullable
              as List<WorkoutSummary>,
      todayProgress: null == todayProgress
          ? _value.todayProgress
          : todayProgress // ignore: cast_nullable_to_non_nullable
              as TodayProgress,
      currentStreak: null == currentStreak
          ? _value.currentStreak
          : currentStreak // ignore: cast_nullable_to_non_nullable
              as int,
      totalPoints: null == totalPoints
          ? _value.totalPoints
          : totalPoints // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  /// Create a copy of HomeData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserStatsCopyWith<$Res> get userStats {
    return $UserStatsCopyWith<$Res>(_value.userStats, (value) {
      return _then(_value.copyWith(userStats: value) as $Val);
    });
  }

  /// Create a copy of HomeData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TodayProgressCopyWith<$Res> get todayProgress {
    return $TodayProgressCopyWith<$Res>(_value.todayProgress, (value) {
      return _then(_value.copyWith(todayProgress: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$HomeDataImplCopyWith<$Res>
    implements $HomeDataCopyWith<$Res> {
  factory _$$HomeDataImplCopyWith(
          _$HomeDataImpl value, $Res Function(_$HomeDataImpl) then) =
      __$$HomeDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {UserStats userStats,
      List<WorkoutSummary> recentWorkouts,
      List<WorkoutSummary> recommendedWorkouts,
      TodayProgress todayProgress,
      int currentStreak,
      int totalPoints});

  @override
  $UserStatsCopyWith<$Res> get userStats;
  @override
  $TodayProgressCopyWith<$Res> get todayProgress;
}

/// @nodoc
class __$$HomeDataImplCopyWithImpl<$Res>
    extends _$HomeDataCopyWithImpl<$Res, _$HomeDataImpl>
    implements _$$HomeDataImplCopyWith<$Res> {
  __$$HomeDataImplCopyWithImpl(
      _$HomeDataImpl _value, $Res Function(_$HomeDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of HomeData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userStats = null,
    Object? recentWorkouts = null,
    Object? recommendedWorkouts = null,
    Object? todayProgress = null,
    Object? currentStreak = null,
    Object? totalPoints = null,
  }) {
    return _then(_$HomeDataImpl(
      userStats: null == userStats
          ? _value.userStats
          : userStats // ignore: cast_nullable_to_non_nullable
              as UserStats,
      recentWorkouts: null == recentWorkouts
          ? _value._recentWorkouts
          : recentWorkouts // ignore: cast_nullable_to_non_nullable
              as List<WorkoutSummary>,
      recommendedWorkouts: null == recommendedWorkouts
          ? _value._recommendedWorkouts
          : recommendedWorkouts // ignore: cast_nullable_to_non_nullable
              as List<WorkoutSummary>,
      todayProgress: null == todayProgress
          ? _value.todayProgress
          : todayProgress // ignore: cast_nullable_to_non_nullable
              as TodayProgress,
      currentStreak: null == currentStreak
          ? _value.currentStreak
          : currentStreak // ignore: cast_nullable_to_non_nullable
              as int,
      totalPoints: null == totalPoints
          ? _value.totalPoints
          : totalPoints // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$HomeDataImpl implements _HomeData {
  const _$HomeDataImpl(
      {required this.userStats,
      required final List<WorkoutSummary> recentWorkouts,
      required final List<WorkoutSummary> recommendedWorkouts,
      required this.todayProgress,
      required this.currentStreak,
      required this.totalPoints})
      : _recentWorkouts = recentWorkouts,
        _recommendedWorkouts = recommendedWorkouts;

  factory _$HomeDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$HomeDataImplFromJson(json);

  @override
  final UserStats userStats;
  final List<WorkoutSummary> _recentWorkouts;
  @override
  List<WorkoutSummary> get recentWorkouts {
    if (_recentWorkouts is EqualUnmodifiableListView) return _recentWorkouts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_recentWorkouts);
  }

  final List<WorkoutSummary> _recommendedWorkouts;
  @override
  List<WorkoutSummary> get recommendedWorkouts {
    if (_recommendedWorkouts is EqualUnmodifiableListView)
      return _recommendedWorkouts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_recommendedWorkouts);
  }

  @override
  final TodayProgress todayProgress;
  @override
  final int currentStreak;
  @override
  final int totalPoints;

  @override
  String toString() {
    return 'HomeData(userStats: $userStats, recentWorkouts: $recentWorkouts, recommendedWorkouts: $recommendedWorkouts, todayProgress: $todayProgress, currentStreak: $currentStreak, totalPoints: $totalPoints)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HomeDataImpl &&
            (identical(other.userStats, userStats) ||
                other.userStats == userStats) &&
            const DeepCollectionEquality()
                .equals(other._recentWorkouts, _recentWorkouts) &&
            const DeepCollectionEquality()
                .equals(other._recommendedWorkouts, _recommendedWorkouts) &&
            (identical(other.todayProgress, todayProgress) ||
                other.todayProgress == todayProgress) &&
            (identical(other.currentStreak, currentStreak) ||
                other.currentStreak == currentStreak) &&
            (identical(other.totalPoints, totalPoints) ||
                other.totalPoints == totalPoints));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      userStats,
      const DeepCollectionEquality().hash(_recentWorkouts),
      const DeepCollectionEquality().hash(_recommendedWorkouts),
      todayProgress,
      currentStreak,
      totalPoints);

  /// Create a copy of HomeData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HomeDataImplCopyWith<_$HomeDataImpl> get copyWith =>
      __$$HomeDataImplCopyWithImpl<_$HomeDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$HomeDataImplToJson(
      this,
    );
  }
}

abstract class _HomeData implements HomeData {
  const factory _HomeData(
      {required final UserStats userStats,
      required final List<WorkoutSummary> recentWorkouts,
      required final List<WorkoutSummary> recommendedWorkouts,
      required final TodayProgress todayProgress,
      required final int currentStreak,
      required final int totalPoints}) = _$HomeDataImpl;

  factory _HomeData.fromJson(Map<String, dynamic> json) =
      _$HomeDataImpl.fromJson;

  @override
  UserStats get userStats;
  @override
  List<WorkoutSummary> get recentWorkouts;
  @override
  List<WorkoutSummary> get recommendedWorkouts;
  @override
  TodayProgress get todayProgress;
  @override
  int get currentStreak;
  @override
  int get totalPoints;

  /// Create a copy of HomeData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HomeDataImplCopyWith<_$HomeDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

UserStats _$UserStatsFromJson(Map<String, dynamic> json) {
  return _UserStats.fromJson(json);
}

/// @nodoc
mixin _$UserStats {
  int get totalWorkouts => throw _privateConstructorUsedError;
  int get totalMinutes => throw _privateConstructorUsedError;
  int get totalCalories => throw _privateConstructorUsedError;
  double get avgWorkoutDuration => throw _privateConstructorUsedError;
  int get workoutsThisWeek => throw _privateConstructorUsedError;
  int get workoutsThisMonth => throw _privateConstructorUsedError;

  /// Serializes this UserStats to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserStatsCopyWith<UserStats> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserStatsCopyWith<$Res> {
  factory $UserStatsCopyWith(UserStats value, $Res Function(UserStats) then) =
      _$UserStatsCopyWithImpl<$Res, UserStats>;
  @useResult
  $Res call(
      {int totalWorkouts,
      int totalMinutes,
      int totalCalories,
      double avgWorkoutDuration,
      int workoutsThisWeek,
      int workoutsThisMonth});
}

/// @nodoc
class _$UserStatsCopyWithImpl<$Res, $Val extends UserStats>
    implements $UserStatsCopyWith<$Res> {
  _$UserStatsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalWorkouts = null,
    Object? totalMinutes = null,
    Object? totalCalories = null,
    Object? avgWorkoutDuration = null,
    Object? workoutsThisWeek = null,
    Object? workoutsThisMonth = null,
  }) {
    return _then(_value.copyWith(
      totalWorkouts: null == totalWorkouts
          ? _value.totalWorkouts
          : totalWorkouts // ignore: cast_nullable_to_non_nullable
              as int,
      totalMinutes: null == totalMinutes
          ? _value.totalMinutes
          : totalMinutes // ignore: cast_nullable_to_non_nullable
              as int,
      totalCalories: null == totalCalories
          ? _value.totalCalories
          : totalCalories // ignore: cast_nullable_to_non_nullable
              as int,
      avgWorkoutDuration: null == avgWorkoutDuration
          ? _value.avgWorkoutDuration
          : avgWorkoutDuration // ignore: cast_nullable_to_non_nullable
              as double,
      workoutsThisWeek: null == workoutsThisWeek
          ? _value.workoutsThisWeek
          : workoutsThisWeek // ignore: cast_nullable_to_non_nullable
              as int,
      workoutsThisMonth: null == workoutsThisMonth
          ? _value.workoutsThisMonth
          : workoutsThisMonth // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserStatsImplCopyWith<$Res>
    implements $UserStatsCopyWith<$Res> {
  factory _$$UserStatsImplCopyWith(
          _$UserStatsImpl value, $Res Function(_$UserStatsImpl) then) =
      __$$UserStatsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int totalWorkouts,
      int totalMinutes,
      int totalCalories,
      double avgWorkoutDuration,
      int workoutsThisWeek,
      int workoutsThisMonth});
}

/// @nodoc
class __$$UserStatsImplCopyWithImpl<$Res>
    extends _$UserStatsCopyWithImpl<$Res, _$UserStatsImpl>
    implements _$$UserStatsImplCopyWith<$Res> {
  __$$UserStatsImplCopyWithImpl(
      _$UserStatsImpl _value, $Res Function(_$UserStatsImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalWorkouts = null,
    Object? totalMinutes = null,
    Object? totalCalories = null,
    Object? avgWorkoutDuration = null,
    Object? workoutsThisWeek = null,
    Object? workoutsThisMonth = null,
  }) {
    return _then(_$UserStatsImpl(
      totalWorkouts: null == totalWorkouts
          ? _value.totalWorkouts
          : totalWorkouts // ignore: cast_nullable_to_non_nullable
              as int,
      totalMinutes: null == totalMinutes
          ? _value.totalMinutes
          : totalMinutes // ignore: cast_nullable_to_non_nullable
              as int,
      totalCalories: null == totalCalories
          ? _value.totalCalories
          : totalCalories // ignore: cast_nullable_to_non_nullable
              as int,
      avgWorkoutDuration: null == avgWorkoutDuration
          ? _value.avgWorkoutDuration
          : avgWorkoutDuration // ignore: cast_nullable_to_non_nullable
              as double,
      workoutsThisWeek: null == workoutsThisWeek
          ? _value.workoutsThisWeek
          : workoutsThisWeek // ignore: cast_nullable_to_non_nullable
              as int,
      workoutsThisMonth: null == workoutsThisMonth
          ? _value.workoutsThisMonth
          : workoutsThisMonth // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserStatsImpl implements _UserStats {
  const _$UserStatsImpl(
      {required this.totalWorkouts,
      required this.totalMinutes,
      required this.totalCalories,
      required this.avgWorkoutDuration,
      required this.workoutsThisWeek,
      required this.workoutsThisMonth});

  factory _$UserStatsImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserStatsImplFromJson(json);

  @override
  final int totalWorkouts;
  @override
  final int totalMinutes;
  @override
  final int totalCalories;
  @override
  final double avgWorkoutDuration;
  @override
  final int workoutsThisWeek;
  @override
  final int workoutsThisMonth;

  @override
  String toString() {
    return 'UserStats(totalWorkouts: $totalWorkouts, totalMinutes: $totalMinutes, totalCalories: $totalCalories, avgWorkoutDuration: $avgWorkoutDuration, workoutsThisWeek: $workoutsThisWeek, workoutsThisMonth: $workoutsThisMonth)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserStatsImpl &&
            (identical(other.totalWorkouts, totalWorkouts) ||
                other.totalWorkouts == totalWorkouts) &&
            (identical(other.totalMinutes, totalMinutes) ||
                other.totalMinutes == totalMinutes) &&
            (identical(other.totalCalories, totalCalories) ||
                other.totalCalories == totalCalories) &&
            (identical(other.avgWorkoutDuration, avgWorkoutDuration) ||
                other.avgWorkoutDuration == avgWorkoutDuration) &&
            (identical(other.workoutsThisWeek, workoutsThisWeek) ||
                other.workoutsThisWeek == workoutsThisWeek) &&
            (identical(other.workoutsThisMonth, workoutsThisMonth) ||
                other.workoutsThisMonth == workoutsThisMonth));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, totalWorkouts, totalMinutes,
      totalCalories, avgWorkoutDuration, workoutsThisWeek, workoutsThisMonth);

  /// Create a copy of UserStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserStatsImplCopyWith<_$UserStatsImpl> get copyWith =>
      __$$UserStatsImplCopyWithImpl<_$UserStatsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserStatsImplToJson(
      this,
    );
  }
}

abstract class _UserStats implements UserStats {
  const factory _UserStats(
      {required final int totalWorkouts,
      required final int totalMinutes,
      required final int totalCalories,
      required final double avgWorkoutDuration,
      required final int workoutsThisWeek,
      required final int workoutsThisMonth}) = _$UserStatsImpl;

  factory _UserStats.fromJson(Map<String, dynamic> json) =
      _$UserStatsImpl.fromJson;

  @override
  int get totalWorkouts;
  @override
  int get totalMinutes;
  @override
  int get totalCalories;
  @override
  double get avgWorkoutDuration;
  @override
  int get workoutsThisWeek;
  @override
  int get workoutsThisMonth;

  /// Create a copy of UserStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserStatsImplCopyWith<_$UserStatsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

WorkoutSummary _$WorkoutSummaryFromJson(Map<String, dynamic> json) {
  return _WorkoutSummary.fromJson(json);
}

/// @nodoc
mixin _$WorkoutSummary {
  String get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  String get category => throw _privateConstructorUsedError;
  int get duration => throw _privateConstructorUsedError;
  String get difficulty => throw _privateConstructorUsedError;
  int get exercises => throw _privateConstructorUsedError;
  int get calories => throw _privateConstructorUsedError;
  double get rating => throw _privateConstructorUsedError;
  List<String> get equipment => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  DateTime? get lastCompleted => throw _privateConstructorUsedError;
  bool get isCompleted => throw _privateConstructorUsedError;

  /// Serializes this WorkoutSummary to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of WorkoutSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $WorkoutSummaryCopyWith<WorkoutSummary> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $WorkoutSummaryCopyWith<$Res> {
  factory $WorkoutSummaryCopyWith(
          WorkoutSummary value, $Res Function(WorkoutSummary) then) =
      _$WorkoutSummaryCopyWithImpl<$Res, WorkoutSummary>;
  @useResult
  $Res call(
      {String id,
      String name,
      String category,
      int duration,
      String difficulty,
      int exercises,
      int calories,
      double rating,
      List<String> equipment,
      String? imageUrl,
      DateTime? lastCompleted,
      bool isCompleted});
}

/// @nodoc
class _$WorkoutSummaryCopyWithImpl<$Res, $Val extends WorkoutSummary>
    implements $WorkoutSummaryCopyWith<$Res> {
  _$WorkoutSummaryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of WorkoutSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? category = null,
    Object? duration = null,
    Object? difficulty = null,
    Object? exercises = null,
    Object? calories = null,
    Object? rating = null,
    Object? equipment = null,
    Object? imageUrl = freezed,
    Object? lastCompleted = freezed,
    Object? isCompleted = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
      exercises: null == exercises
          ? _value.exercises
          : exercises // ignore: cast_nullable_to_non_nullable
              as int,
      calories: null == calories
          ? _value.calories
          : calories // ignore: cast_nullable_to_non_nullable
              as int,
      rating: null == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as double,
      equipment: null == equipment
          ? _value.equipment
          : equipment // ignore: cast_nullable_to_non_nullable
              as List<String>,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      lastCompleted: freezed == lastCompleted
          ? _value.lastCompleted
          : lastCompleted // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isCompleted: null == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$WorkoutSummaryImplCopyWith<$Res>
    implements $WorkoutSummaryCopyWith<$Res> {
  factory _$$WorkoutSummaryImplCopyWith(_$WorkoutSummaryImpl value,
          $Res Function(_$WorkoutSummaryImpl) then) =
      __$$WorkoutSummaryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String name,
      String category,
      int duration,
      String difficulty,
      int exercises,
      int calories,
      double rating,
      List<String> equipment,
      String? imageUrl,
      DateTime? lastCompleted,
      bool isCompleted});
}

/// @nodoc
class __$$WorkoutSummaryImplCopyWithImpl<$Res>
    extends _$WorkoutSummaryCopyWithImpl<$Res, _$WorkoutSummaryImpl>
    implements _$$WorkoutSummaryImplCopyWith<$Res> {
  __$$WorkoutSummaryImplCopyWithImpl(
      _$WorkoutSummaryImpl _value, $Res Function(_$WorkoutSummaryImpl) _then)
      : super(_value, _then);

  /// Create a copy of WorkoutSummary
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? category = null,
    Object? duration = null,
    Object? difficulty = null,
    Object? exercises = null,
    Object? calories = null,
    Object? rating = null,
    Object? equipment = null,
    Object? imageUrl = freezed,
    Object? lastCompleted = freezed,
    Object? isCompleted = null,
  }) {
    return _then(_$WorkoutSummaryImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      category: null == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as String,
      duration: null == duration
          ? _value.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as int,
      difficulty: null == difficulty
          ? _value.difficulty
          : difficulty // ignore: cast_nullable_to_non_nullable
              as String,
      exercises: null == exercises
          ? _value.exercises
          : exercises // ignore: cast_nullable_to_non_nullable
              as int,
      calories: null == calories
          ? _value.calories
          : calories // ignore: cast_nullable_to_non_nullable
              as int,
      rating: null == rating
          ? _value.rating
          : rating // ignore: cast_nullable_to_non_nullable
              as double,
      equipment: null == equipment
          ? _value._equipment
          : equipment // ignore: cast_nullable_to_non_nullable
              as List<String>,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      lastCompleted: freezed == lastCompleted
          ? _value.lastCompleted
          : lastCompleted // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isCompleted: null == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$WorkoutSummaryImpl implements _WorkoutSummary {
  const _$WorkoutSummaryImpl(
      {required this.id,
      required this.name,
      required this.category,
      required this.duration,
      required this.difficulty,
      required this.exercises,
      required this.calories,
      required this.rating,
      required final List<String> equipment,
      this.imageUrl,
      this.lastCompleted,
      this.isCompleted = false})
      : _equipment = equipment;

  factory _$WorkoutSummaryImpl.fromJson(Map<String, dynamic> json) =>
      _$$WorkoutSummaryImplFromJson(json);

  @override
  final String id;
  @override
  final String name;
  @override
  final String category;
  @override
  final int duration;
  @override
  final String difficulty;
  @override
  final int exercises;
  @override
  final int calories;
  @override
  final double rating;
  final List<String> _equipment;
  @override
  List<String> get equipment {
    if (_equipment is EqualUnmodifiableListView) return _equipment;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_equipment);
  }

  @override
  final String? imageUrl;
  @override
  final DateTime? lastCompleted;
  @override
  @JsonKey()
  final bool isCompleted;

  @override
  String toString() {
    return 'WorkoutSummary(id: $id, name: $name, category: $category, duration: $duration, difficulty: $difficulty, exercises: $exercises, calories: $calories, rating: $rating, equipment: $equipment, imageUrl: $imageUrl, lastCompleted: $lastCompleted, isCompleted: $isCompleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WorkoutSummaryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.difficulty, difficulty) ||
                other.difficulty == difficulty) &&
            (identical(other.exercises, exercises) ||
                other.exercises == exercises) &&
            (identical(other.calories, calories) ||
                other.calories == calories) &&
            (identical(other.rating, rating) || other.rating == rating) &&
            const DeepCollectionEquality()
                .equals(other._equipment, _equipment) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.lastCompleted, lastCompleted) ||
                other.lastCompleted == lastCompleted) &&
            (identical(other.isCompleted, isCompleted) ||
                other.isCompleted == isCompleted));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      category,
      duration,
      difficulty,
      exercises,
      calories,
      rating,
      const DeepCollectionEquality().hash(_equipment),
      imageUrl,
      lastCompleted,
      isCompleted);

  /// Create a copy of WorkoutSummary
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WorkoutSummaryImplCopyWith<_$WorkoutSummaryImpl> get copyWith =>
      __$$WorkoutSummaryImplCopyWithImpl<_$WorkoutSummaryImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$WorkoutSummaryImplToJson(
      this,
    );
  }
}

abstract class _WorkoutSummary implements WorkoutSummary {
  const factory _WorkoutSummary(
      {required final String id,
      required final String name,
      required final String category,
      required final int duration,
      required final String difficulty,
      required final int exercises,
      required final int calories,
      required final double rating,
      required final List<String> equipment,
      final String? imageUrl,
      final DateTime? lastCompleted,
      final bool isCompleted}) = _$WorkoutSummaryImpl;

  factory _WorkoutSummary.fromJson(Map<String, dynamic> json) =
      _$WorkoutSummaryImpl.fromJson;

  @override
  String get id;
  @override
  String get name;
  @override
  String get category;
  @override
  int get duration;
  @override
  String get difficulty;
  @override
  int get exercises;
  @override
  int get calories;
  @override
  double get rating;
  @override
  List<String> get equipment;
  @override
  String? get imageUrl;
  @override
  DateTime? get lastCompleted;
  @override
  bool get isCompleted;

  /// Create a copy of WorkoutSummary
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WorkoutSummaryImplCopyWith<_$WorkoutSummaryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TodayProgress _$TodayProgressFromJson(Map<String, dynamic> json) {
  return _TodayProgress.fromJson(json);
}

/// @nodoc
mixin _$TodayProgress {
  int get calories => throw _privateConstructorUsedError;
  int get caloriesGoal => throw _privateConstructorUsedError;
  int get steps => throw _privateConstructorUsedError;
  int get stepsGoal => throw _privateConstructorUsedError;
  int get activeMinutes => throw _privateConstructorUsedError;
  int get activeMinutesGoal => throw _privateConstructorUsedError;
  int get heartRate => throw _privateConstructorUsedError;
  int get waterIntake => throw _privateConstructorUsedError;
  int get waterIntakeGoal => throw _privateConstructorUsedError;

  /// Serializes this TodayProgress to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TodayProgress
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TodayProgressCopyWith<TodayProgress> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TodayProgressCopyWith<$Res> {
  factory $TodayProgressCopyWith(
          TodayProgress value, $Res Function(TodayProgress) then) =
      _$TodayProgressCopyWithImpl<$Res, TodayProgress>;
  @useResult
  $Res call(
      {int calories,
      int caloriesGoal,
      int steps,
      int stepsGoal,
      int activeMinutes,
      int activeMinutesGoal,
      int heartRate,
      int waterIntake,
      int waterIntakeGoal});
}

/// @nodoc
class _$TodayProgressCopyWithImpl<$Res, $Val extends TodayProgress>
    implements $TodayProgressCopyWith<$Res> {
  _$TodayProgressCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TodayProgress
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? calories = null,
    Object? caloriesGoal = null,
    Object? steps = null,
    Object? stepsGoal = null,
    Object? activeMinutes = null,
    Object? activeMinutesGoal = null,
    Object? heartRate = null,
    Object? waterIntake = null,
    Object? waterIntakeGoal = null,
  }) {
    return _then(_value.copyWith(
      calories: null == calories
          ? _value.calories
          : calories // ignore: cast_nullable_to_non_nullable
              as int,
      caloriesGoal: null == caloriesGoal
          ? _value.caloriesGoal
          : caloriesGoal // ignore: cast_nullable_to_non_nullable
              as int,
      steps: null == steps
          ? _value.steps
          : steps // ignore: cast_nullable_to_non_nullable
              as int,
      stepsGoal: null == stepsGoal
          ? _value.stepsGoal
          : stepsGoal // ignore: cast_nullable_to_non_nullable
              as int,
      activeMinutes: null == activeMinutes
          ? _value.activeMinutes
          : activeMinutes // ignore: cast_nullable_to_non_nullable
              as int,
      activeMinutesGoal: null == activeMinutesGoal
          ? _value.activeMinutesGoal
          : activeMinutesGoal // ignore: cast_nullable_to_non_nullable
              as int,
      heartRate: null == heartRate
          ? _value.heartRate
          : heartRate // ignore: cast_nullable_to_non_nullable
              as int,
      waterIntake: null == waterIntake
          ? _value.waterIntake
          : waterIntake // ignore: cast_nullable_to_non_nullable
              as int,
      waterIntakeGoal: null == waterIntakeGoal
          ? _value.waterIntakeGoal
          : waterIntakeGoal // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TodayProgressImplCopyWith<$Res>
    implements $TodayProgressCopyWith<$Res> {
  factory _$$TodayProgressImplCopyWith(
          _$TodayProgressImpl value, $Res Function(_$TodayProgressImpl) then) =
      __$$TodayProgressImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int calories,
      int caloriesGoal,
      int steps,
      int stepsGoal,
      int activeMinutes,
      int activeMinutesGoal,
      int heartRate,
      int waterIntake,
      int waterIntakeGoal});
}

/// @nodoc
class __$$TodayProgressImplCopyWithImpl<$Res>
    extends _$TodayProgressCopyWithImpl<$Res, _$TodayProgressImpl>
    implements _$$TodayProgressImplCopyWith<$Res> {
  __$$TodayProgressImplCopyWithImpl(
      _$TodayProgressImpl _value, $Res Function(_$TodayProgressImpl) _then)
      : super(_value, _then);

  /// Create a copy of TodayProgress
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? calories = null,
    Object? caloriesGoal = null,
    Object? steps = null,
    Object? stepsGoal = null,
    Object? activeMinutes = null,
    Object? activeMinutesGoal = null,
    Object? heartRate = null,
    Object? waterIntake = null,
    Object? waterIntakeGoal = null,
  }) {
    return _then(_$TodayProgressImpl(
      calories: null == calories
          ? _value.calories
          : calories // ignore: cast_nullable_to_non_nullable
              as int,
      caloriesGoal: null == caloriesGoal
          ? _value.caloriesGoal
          : caloriesGoal // ignore: cast_nullable_to_non_nullable
              as int,
      steps: null == steps
          ? _value.steps
          : steps // ignore: cast_nullable_to_non_nullable
              as int,
      stepsGoal: null == stepsGoal
          ? _value.stepsGoal
          : stepsGoal // ignore: cast_nullable_to_non_nullable
              as int,
      activeMinutes: null == activeMinutes
          ? _value.activeMinutes
          : activeMinutes // ignore: cast_nullable_to_non_nullable
              as int,
      activeMinutesGoal: null == activeMinutesGoal
          ? _value.activeMinutesGoal
          : activeMinutesGoal // ignore: cast_nullable_to_non_nullable
              as int,
      heartRate: null == heartRate
          ? _value.heartRate
          : heartRate // ignore: cast_nullable_to_non_nullable
              as int,
      waterIntake: null == waterIntake
          ? _value.waterIntake
          : waterIntake // ignore: cast_nullable_to_non_nullable
              as int,
      waterIntakeGoal: null == waterIntakeGoal
          ? _value.waterIntakeGoal
          : waterIntakeGoal // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TodayProgressImpl implements _TodayProgress {
  const _$TodayProgressImpl(
      {required this.calories,
      required this.caloriesGoal,
      required this.steps,
      required this.stepsGoal,
      required this.activeMinutes,
      required this.activeMinutesGoal,
      required this.heartRate,
      required this.waterIntake,
      required this.waterIntakeGoal});

  factory _$TodayProgressImpl.fromJson(Map<String, dynamic> json) =>
      _$$TodayProgressImplFromJson(json);

  @override
  final int calories;
  @override
  final int caloriesGoal;
  @override
  final int steps;
  @override
  final int stepsGoal;
  @override
  final int activeMinutes;
  @override
  final int activeMinutesGoal;
  @override
  final int heartRate;
  @override
  final int waterIntake;
  @override
  final int waterIntakeGoal;

  @override
  String toString() {
    return 'TodayProgress(calories: $calories, caloriesGoal: $caloriesGoal, steps: $steps, stepsGoal: $stepsGoal, activeMinutes: $activeMinutes, activeMinutesGoal: $activeMinutesGoal, heartRate: $heartRate, waterIntake: $waterIntake, waterIntakeGoal: $waterIntakeGoal)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TodayProgressImpl &&
            (identical(other.calories, calories) ||
                other.calories == calories) &&
            (identical(other.caloriesGoal, caloriesGoal) ||
                other.caloriesGoal == caloriesGoal) &&
            (identical(other.steps, steps) || other.steps == steps) &&
            (identical(other.stepsGoal, stepsGoal) ||
                other.stepsGoal == stepsGoal) &&
            (identical(other.activeMinutes, activeMinutes) ||
                other.activeMinutes == activeMinutes) &&
            (identical(other.activeMinutesGoal, activeMinutesGoal) ||
                other.activeMinutesGoal == activeMinutesGoal) &&
            (identical(other.heartRate, heartRate) ||
                other.heartRate == heartRate) &&
            (identical(other.waterIntake, waterIntake) ||
                other.waterIntake == waterIntake) &&
            (identical(other.waterIntakeGoal, waterIntakeGoal) ||
                other.waterIntakeGoal == waterIntakeGoal));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      calories,
      caloriesGoal,
      steps,
      stepsGoal,
      activeMinutes,
      activeMinutesGoal,
      heartRate,
      waterIntake,
      waterIntakeGoal);

  /// Create a copy of TodayProgress
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TodayProgressImplCopyWith<_$TodayProgressImpl> get copyWith =>
      __$$TodayProgressImplCopyWithImpl<_$TodayProgressImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TodayProgressImplToJson(
      this,
    );
  }
}

abstract class _TodayProgress implements TodayProgress {
  const factory _TodayProgress(
      {required final int calories,
      required final int caloriesGoal,
      required final int steps,
      required final int stepsGoal,
      required final int activeMinutes,
      required final int activeMinutesGoal,
      required final int heartRate,
      required final int waterIntake,
      required final int waterIntakeGoal}) = _$TodayProgressImpl;

  factory _TodayProgress.fromJson(Map<String, dynamic> json) =
      _$TodayProgressImpl.fromJson;

  @override
  int get calories;
  @override
  int get caloriesGoal;
  @override
  int get steps;
  @override
  int get stepsGoal;
  @override
  int get activeMinutes;
  @override
  int get activeMinutesGoal;
  @override
  int get heartRate;
  @override
  int get waterIntake;
  @override
  int get waterIntakeGoal;

  /// Create a copy of TodayProgress
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TodayProgressImplCopyWith<_$TodayProgressImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
