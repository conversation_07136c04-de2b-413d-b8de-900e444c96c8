import 'package:freezed_annotation/freezed_annotation.dart';

part 'home_data.freezed.dart';
part 'home_data.g.dart';

@freezed
class HomeData with _$HomeData {
  const factory HomeData({
    required UserStats userStats,
    required List<WorkoutSummary> recentWorkouts,
    required List<WorkoutSummary> recommendedWorkouts,
    required TodayProgress todayProgress,
    required int currentStreak,
    required int totalPoints,
  }) = _HomeData;

  factory HomeData.fromJson(Map<String, dynamic> json) =>
      _$HomeDataFromJson(json);
}

@freezed
class UserStats with _$UserStats {
  const factory UserStats({
    required int totalWorkouts,
    required int totalMinutes,
    required int totalCalories,
    required double avgWorkoutDuration,
    required int workoutsThisWeek,
    required int workoutsThisMonth,
  }) = _UserStats;

  factory UserStats.fromJson(Map<String, dynamic> json) =>
      _$UserStatsFromJson(json);
}

@freezed
class WorkoutSummary with _$WorkoutSummary {
  const factory WorkoutSummary({
    required String id,
    required String name,
    required String category,
    required int duration,
    required String difficulty,
    required int exercises,
    required int calories,
    required double rating,
    required List<String> equipment,
    String? imageUrl,
    DateTime? lastCompleted,
    @Default(false) bool isCompleted,
  }) = _WorkoutSummary;

  factory WorkoutSummary.fromJson(Map<String, dynamic> json) =>
      _$WorkoutSummaryFromJson(json);
}

@freezed
class TodayProgress with _$TodayProgress {
  const factory TodayProgress({
    required int calories,
    required int caloriesGoal,
    required int steps,
    required int stepsGoal,
    required int activeMinutes,
    required int activeMinutesGoal,
    required int heartRate,
    required int waterIntake,
    required int waterIntakeGoal,
  }) = _TodayProgress;

  factory TodayProgress.fromJson(Map<String, dynamic> json) =>
      _$TodayProgressFromJson(json);
}