import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/home_data.dart';

class HomeRepository {
  final SupabaseClient _supabase;

  HomeRepository(this._supabase);

  Future<HomeData> getHomeData(String userId) async {
    try {
      // Fetch user stats
      final userStats = await _getUserStats(userId);
      
      // Fetch recent workouts
      final recentWorkouts = await _getRecentWorkouts(userId);
      
      // Fetch recommended workouts
      final recommendedWorkouts = await _getRecommendedWorkouts(userId);
      
      // Fetch today's progress
      final todayProgress = await _getTodayProgress(userId);
      
      // Calculate streak and points
      final streakData = await _getStreakAndPoints(userId);
      
      return HomeData(
        userStats: userStats,
        recentWorkouts: recentWorkouts,
        recommendedWorkouts: recommendedWorkouts,
        todayProgress: todayProgress,
        currentStreak: streakData['streak'] ?? 0,
        totalPoints: streakData['points'] ?? 0,
      );
    } catch (e) {
      throw Exception('Failed to load home data: $e');
    }
  }

  Future<UserStats> _getUserStats(String userId) async {
    final response = await _supabase
        .from('completed_workouts')
        .select()
        .eq('user_id', userId);
    
    final workouts = List<Map<String, dynamic>>.from(response);
    
    // Calculate stats
    final totalWorkouts = workouts.length;
    final totalMinutes = workouts.fold<int>(
      0, (sum, workout) => sum + (workout['duration'] as int? ?? 0));
    final totalCalories = workouts.fold<int>(
      0, (sum, workout) => sum + (workout['calories_burned'] as int? ?? 0));
    
    // Calculate this week's workouts
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    final workoutsThisWeek = workouts.where((workout) {
      final date = DateTime.parse(workout['date_completed']);
      return date.isAfter(weekStart);
    }).length;
    
    // Calculate this month's workouts
    final monthStart = DateTime(now.year, now.month, 1);
    final workoutsThisMonth = workouts.where((workout) {
      final date = DateTime.parse(workout['date_completed']);
      return date.isAfter(monthStart);
    }).length;
    
    return UserStats(
      totalWorkouts: totalWorkouts,
      totalMinutes: totalMinutes,
      totalCalories: totalCalories,
      avgWorkoutDuration: totalWorkouts > 0 ? totalMinutes / totalWorkouts : 0,
      workoutsThisWeek: workoutsThisWeek,
      workoutsThisMonth: workoutsThisMonth,
    );
  }

  Future<List<WorkoutSummary>> _getRecentWorkouts(String userId) async {
    final response = await _supabase
        .from('workouts')
        .select('''
          *,
          workout_exercises(count),
          completed_workouts(date_completed)
        ''')
        .eq('user_id', userId)
        .order('created_at', ascending: false)
        .limit(5);
    
    return (response as List).map((workout) {
      final exerciseCount = workout['workout_exercises']?[0]?['count'] ?? 0;
      final completions = workout['completed_workouts'] as List? ?? [];
      final lastCompleted = completions.isNotEmpty 
          ? DateTime.parse(completions.first['date_completed'])
          : null;
      
      return WorkoutSummary(
        id: workout['id'],
        name: workout['name'],
        category: _categorizeWorkout(workout['name']),
        duration: 30, // Default duration, should be calculated
        difficulty: 'Intermediate', // Should be determined from workout
        exercises: exerciseCount,
        calories: 250, // Should be calculated
        rating: 4.5, // Should come from user ratings
        equipment: [], // Should be extracted from exercises
        lastCompleted: lastCompleted,
        isCompleted: workout['is_completed'] ?? false,
      );
    }).toList();
  }

  Future<List<WorkoutSummary>> _getRecommendedWorkouts(String userId) async {
    // Get user profile to understand preferences
    final profile = await _supabase
        .from('profiles')
        .select()
        .eq('id', userId)
        .single();
    
    final fitnessGoals = List<String>.from(profile['fitness_goals_array'] ?? []);
    final equipment = List<String>.from(profile['equipment'] ?? []);
    final fitnessLevel = profile['fitness_level'] ?? 'Intermediate';
    
    // For now, return mock recommendations
    // In production, this would use AI/ML to generate personalized recommendations
    return [
      WorkoutSummary(
        id: '1',
        name: 'Full Body HIIT',
        category: 'HIIT',
        duration: 30,
        difficulty: fitnessLevel,
        exercises: 8,
        calories: 320,
        rating: 4.8,
        equipment: ['None'],
      ),
      WorkoutSummary(
        id: '2',
        name: 'Strength Training',
        category: 'Strength',
        duration: 45,
        difficulty: fitnessLevel,
        exercises: 10,
        calories: 280,
        rating: 4.7,
        equipment: equipment.take(2).toList(),
      ),
      WorkoutSummary(
        id: '3',
        name: 'Yoga Flow',
        category: 'Yoga',
        duration: 30,
        difficulty: 'Beginner',
        exercises: 15,
        calories: 150,
        rating: 4.9,
        equipment: ['Yoga Mat'],
      ),
    ];
  }

  Future<TodayProgress> _getTodayProgress(String userId) async {
    // In a real app, this would fetch from fitness tracking APIs or devices
    // For now, return mock data
    return const TodayProgress(
      calories: 850,
      caloriesGoal: 1300,
      steps: 8234,
      stepsGoal: 10000,
      activeMinutes: 45,
      activeMinutesGoal: 60,
      heartRate: 125,
      waterIntake: 5,
      waterIntakeGoal: 8,
    );
  }

  Future<Map<String, int>> _getStreakAndPoints(String userId) async {
    final response = await _supabase
        .from('completed_workouts')
        .select('date_completed')
        .eq('user_id', userId)
        .order('date_completed', ascending: false);
    
    final completions = List<Map<String, dynamic>>.from(response);
    
    // Calculate streak
    int streak = 0;
    if (completions.isNotEmpty) {
      DateTime? lastDate;
      for (final completion in completions) {
        final date = DateTime.parse(completion['date_completed']);
        final dateOnly = DateTime(date.year, date.month, date.day);
        
        if (lastDate == null) {
          // First workout
          final today = DateTime.now();
          final todayOnly = DateTime(today.year, today.month, today.day);
          if (dateOnly == todayOnly || 
              dateOnly == todayOnly.subtract(const Duration(days: 1))) {
            streak = 1;
            lastDate = dateOnly;
          } else {
            break;
          }
        } else {
          // Check if consecutive day
          if (lastDate.subtract(const Duration(days: 1)) == dateOnly) {
            streak++;
            lastDate = dateOnly;
          } else {
            break;
          }
        }
      }
    }
    
    // Calculate points (10 points per workout + 50 bonus per week streak)
    final points = completions.length * 10 + (streak ~/ 7) * 50;
    
    return {'streak': streak, 'points': points};
  }

  String _categorizeWorkout(String workoutName) {
    final name = workoutName.toLowerCase();
    if (name.contains('strength') || name.contains('weight')) return 'Strength';
    if (name.contains('cardio') || name.contains('run')) return 'Cardio';
    if (name.contains('hiit')) return 'HIIT';
    if (name.contains('yoga') || name.contains('stretch')) return 'Yoga';
    return 'General';
  }
}