// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_profile.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$UserProfileImpl _$$UserProfileImplFromJson(Map<String, dynamic> json) =>
    _$UserProfileImpl(
      id: json['id'] as String,
      email: json['email'] as String?,
      displayName: json['displayName'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      gender: json['gender'] as String?,
      age: (json['age'] as num?)?.toInt(),
      height: (json['height'] as num?)?.toDouble(),
      heightUnit: json['heightUnit'] as String? ?? 'cm',
      weight: (json['weight'] as num?)?.toDouble(),
      weightUnit: json['weightUnit'] as String? ?? 'kg',
      fitnessGoalPrimary: json['fitnessGoalPrimary'] as String?,
      fitnessGoalsArray: (json['fitnessGoalsArray'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      fitnessGoalsOrder: (json['fitnessGoalsOrder'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      trainingExperienceLevel: json['trainingExperienceLevel'] as String?,
      fitnessExperience: json['fitnessExperience'] as String?,
      fitnessLevel: (json['fitnessLevel'] as num?)?.toInt(),
      cardioFitnessLevel: (json['cardioFitnessLevel'] as num?)?.toInt(),
      weightliftingFitnessLevel:
          (json['weightliftingFitnessLevel'] as num?)?.toInt(),
      cardioLevel: json['cardioLevel'] as String?,
      cardioLevelDescription: json['cardioLevelDescription'] as String?,
      weightliftingLevel: json['weightliftingLevel'] as String?,
      weightliftingLevelDescription:
          json['weightliftingLevelDescription'] as String?,
      workoutFrequencyDays: (json['workoutFrequencyDays'] as num?)?.toInt(),
      preferredWorkoutDaysCount:
          (json['preferredWorkoutDaysCount'] as num?)?.toInt(),
      workoutDays: (json['workoutDays'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      workoutDurationPreference: json['workoutDurationPreference'] as String?,
      preferredWorkoutDuration: json['preferredWorkoutDuration'] as String?,
      exercisePreferences: (json['exercisePreferences'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      equipment: (json['equipment'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      workoutEnvironment: (json['workoutEnvironment'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      sportActivity: json['sportActivity'] as String?,
      sportOfChoice: json['sportOfChoice'] as String?,
      specificSportActivity: json['specificSportActivity'] as String?,
      physicalLimitations: (json['physicalLimitations'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      healthConditions: (json['healthConditions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      exercisesToAvoid: (json['exercisesToAvoid'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      excludedExercises: (json['excludedExercises'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      additionalHealthInfo: json['additionalHealthInfo'] as String?,
      dietPreferences: (json['dietPreferences'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      dietaryRestrictions: (json['dietaryRestrictions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      mealsPerDay: (json['mealsPerDay'] as num?)?.toInt(),
      caloricGoal: (json['caloricGoal'] as num?)?.toInt(),
      takingSupplements: json['takingSupplements'] as bool?,
      supplements: (json['supplements'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      sleepQuality: json['sleepQuality'] as String?,
      onboardingCompleted: json['onboardingCompleted'] as bool? ?? false,
      hasCompletedPreferences:
          json['hasCompletedPreferences'] as bool? ?? false,
      fitnessAssessmentCompleted:
          json['fitnessAssessmentCompleted'] as bool? ?? false,
      fitnessGuide: json['fitnessGuide'] as String?,
      additionalNotes: json['additionalNotes'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$$UserProfileImplToJson(_$UserProfileImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'displayName': instance.displayName,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'gender': instance.gender,
      'age': instance.age,
      'height': instance.height,
      'heightUnit': instance.heightUnit,
      'weight': instance.weight,
      'weightUnit': instance.weightUnit,
      'fitnessGoalPrimary': instance.fitnessGoalPrimary,
      'fitnessGoalsArray': instance.fitnessGoalsArray,
      'fitnessGoalsOrder': instance.fitnessGoalsOrder,
      'trainingExperienceLevel': instance.trainingExperienceLevel,
      'fitnessExperience': instance.fitnessExperience,
      'fitnessLevel': instance.fitnessLevel,
      'cardioFitnessLevel': instance.cardioFitnessLevel,
      'weightliftingFitnessLevel': instance.weightliftingFitnessLevel,
      'cardioLevel': instance.cardioLevel,
      'cardioLevelDescription': instance.cardioLevelDescription,
      'weightliftingLevel': instance.weightliftingLevel,
      'weightliftingLevelDescription': instance.weightliftingLevelDescription,
      'workoutFrequencyDays': instance.workoutFrequencyDays,
      'preferredWorkoutDaysCount': instance.preferredWorkoutDaysCount,
      'workoutDays': instance.workoutDays,
      'workoutDurationPreference': instance.workoutDurationPreference,
      'preferredWorkoutDuration': instance.preferredWorkoutDuration,
      'exercisePreferences': instance.exercisePreferences,
      'equipment': instance.equipment,
      'workoutEnvironment': instance.workoutEnvironment,
      'sportActivity': instance.sportActivity,
      'sportOfChoice': instance.sportOfChoice,
      'specificSportActivity': instance.specificSportActivity,
      'physicalLimitations': instance.physicalLimitations,
      'healthConditions': instance.healthConditions,
      'exercisesToAvoid': instance.exercisesToAvoid,
      'excludedExercises': instance.excludedExercises,
      'additionalHealthInfo': instance.additionalHealthInfo,
      'dietPreferences': instance.dietPreferences,
      'dietaryRestrictions': instance.dietaryRestrictions,
      'mealsPerDay': instance.mealsPerDay,
      'caloricGoal': instance.caloricGoal,
      'takingSupplements': instance.takingSupplements,
      'supplements': instance.supplements,
      'sleepQuality': instance.sleepQuality,
      'onboardingCompleted': instance.onboardingCompleted,
      'hasCompletedPreferences': instance.hasCompletedPreferences,
      'fitnessAssessmentCompleted': instance.fitnessAssessmentCompleted,
      'fitnessGuide': instance.fitnessGuide,
      'additionalNotes': instance.additionalNotes,
      'metadata': instance.metadata,
    };
