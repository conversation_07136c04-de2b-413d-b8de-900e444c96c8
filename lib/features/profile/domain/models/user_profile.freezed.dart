// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_profile.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserProfile _$UserProfileFromJson(Map<String, dynamic> json) {
  return _UserProfile.fromJson(json);
}

/// @nodoc
mixin _$UserProfile {
  String get id => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;
  String? get displayName => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get updatedAt =>
      throw _privateConstructorUsedError; // Personal Information
  String? get gender => throw _privateConstructorUsedError;
  int? get age => throw _privateConstructorUsedError;
  double? get height => throw _privateConstructorUsedError;
  String get heightUnit => throw _privateConstructorUsedError;
  double? get weight => throw _privateConstructorUsedError;
  String get weightUnit => throw _privateConstructorUsedError; // Fitness Goals
  String? get fitnessGoalPrimary => throw _privateConstructorUsedError;
  List<String> get fitnessGoalsArray => throw _privateConstructorUsedError;
  List<String> get fitnessGoalsOrder =>
      throw _privateConstructorUsedError; // Fitness Experience & Levels
  String? get trainingExperienceLevel => throw _privateConstructorUsedError;
  String? get fitnessExperience => throw _privateConstructorUsedError;
  int? get fitnessLevel => throw _privateConstructorUsedError;
  int? get cardioFitnessLevel => throw _privateConstructorUsedError;
  int? get weightliftingFitnessLevel => throw _privateConstructorUsedError;
  String? get cardioLevel => throw _privateConstructorUsedError;
  String? get cardioLevelDescription => throw _privateConstructorUsedError;
  String? get weightliftingLevel => throw _privateConstructorUsedError;
  String? get weightliftingLevelDescription =>
      throw _privateConstructorUsedError; // Workout Preferences
  int? get workoutFrequencyDays => throw _privateConstructorUsedError;
  int? get preferredWorkoutDaysCount => throw _privateConstructorUsedError;
  List<String> get workoutDays => throw _privateConstructorUsedError;
  String? get workoutDurationPreference => throw _privateConstructorUsedError;
  String? get preferredWorkoutDuration =>
      throw _privateConstructorUsedError; // Exercise & Equipment
  List<String> get exercisePreferences => throw _privateConstructorUsedError;
  List<String> get equipment => throw _privateConstructorUsedError;
  List<String> get workoutEnvironment => throw _privateConstructorUsedError;
  String? get sportActivity => throw _privateConstructorUsedError;
  String? get sportOfChoice => throw _privateConstructorUsedError;
  String? get specificSportActivity =>
      throw _privateConstructorUsedError; // Limitations & Restrictions
  List<String> get physicalLimitations => throw _privateConstructorUsedError;
  List<String> get healthConditions => throw _privateConstructorUsedError;
  List<String> get exercisesToAvoid => throw _privateConstructorUsedError;
  List<String> get excludedExercises => throw _privateConstructorUsedError;
  String? get additionalHealthInfo =>
      throw _privateConstructorUsedError; // Nutrition & Lifestyle
  List<String> get dietPreferences => throw _privateConstructorUsedError;
  List<String> get dietaryRestrictions => throw _privateConstructorUsedError;
  int? get mealsPerDay => throw _privateConstructorUsedError;
  int? get caloricGoal => throw _privateConstructorUsedError;
  bool? get takingSupplements => throw _privateConstructorUsedError;
  List<String> get supplements => throw _privateConstructorUsedError;
  String? get sleepQuality =>
      throw _privateConstructorUsedError; // Progress Tracking
  bool get onboardingCompleted => throw _privateConstructorUsedError;
  bool get hasCompletedPreferences => throw _privateConstructorUsedError;
  bool get fitnessAssessmentCompleted =>
      throw _privateConstructorUsedError; // Additional Data
  String? get fitnessGuide => throw _privateConstructorUsedError;
  String? get additionalNotes => throw _privateConstructorUsedError;
  Map<String, dynamic>? get metadata => throw _privateConstructorUsedError;

  /// Serializes this UserProfile to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of UserProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UserProfileCopyWith<UserProfile> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserProfileCopyWith<$Res> {
  factory $UserProfileCopyWith(
          UserProfile value, $Res Function(UserProfile) then) =
      _$UserProfileCopyWithImpl<$Res, UserProfile>;
  @useResult
  $Res call(
      {String id,
      String? email,
      String? displayName,
      DateTime createdAt,
      DateTime updatedAt,
      String? gender,
      int? age,
      double? height,
      String heightUnit,
      double? weight,
      String weightUnit,
      String? fitnessGoalPrimary,
      List<String> fitnessGoalsArray,
      List<String> fitnessGoalsOrder,
      String? trainingExperienceLevel,
      String? fitnessExperience,
      int? fitnessLevel,
      int? cardioFitnessLevel,
      int? weightliftingFitnessLevel,
      String? cardioLevel,
      String? cardioLevelDescription,
      String? weightliftingLevel,
      String? weightliftingLevelDescription,
      int? workoutFrequencyDays,
      int? preferredWorkoutDaysCount,
      List<String> workoutDays,
      String? workoutDurationPreference,
      String? preferredWorkoutDuration,
      List<String> exercisePreferences,
      List<String> equipment,
      List<String> workoutEnvironment,
      String? sportActivity,
      String? sportOfChoice,
      String? specificSportActivity,
      List<String> physicalLimitations,
      List<String> healthConditions,
      List<String> exercisesToAvoid,
      List<String> excludedExercises,
      String? additionalHealthInfo,
      List<String> dietPreferences,
      List<String> dietaryRestrictions,
      int? mealsPerDay,
      int? caloricGoal,
      bool? takingSupplements,
      List<String> supplements,
      String? sleepQuality,
      bool onboardingCompleted,
      bool hasCompletedPreferences,
      bool fitnessAssessmentCompleted,
      String? fitnessGuide,
      String? additionalNotes,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class _$UserProfileCopyWithImpl<$Res, $Val extends UserProfile>
    implements $UserProfileCopyWith<$Res> {
  _$UserProfileCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of UserProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = freezed,
    Object? displayName = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? gender = freezed,
    Object? age = freezed,
    Object? height = freezed,
    Object? heightUnit = null,
    Object? weight = freezed,
    Object? weightUnit = null,
    Object? fitnessGoalPrimary = freezed,
    Object? fitnessGoalsArray = null,
    Object? fitnessGoalsOrder = null,
    Object? trainingExperienceLevel = freezed,
    Object? fitnessExperience = freezed,
    Object? fitnessLevel = freezed,
    Object? cardioFitnessLevel = freezed,
    Object? weightliftingFitnessLevel = freezed,
    Object? cardioLevel = freezed,
    Object? cardioLevelDescription = freezed,
    Object? weightliftingLevel = freezed,
    Object? weightliftingLevelDescription = freezed,
    Object? workoutFrequencyDays = freezed,
    Object? preferredWorkoutDaysCount = freezed,
    Object? workoutDays = null,
    Object? workoutDurationPreference = freezed,
    Object? preferredWorkoutDuration = freezed,
    Object? exercisePreferences = null,
    Object? equipment = null,
    Object? workoutEnvironment = null,
    Object? sportActivity = freezed,
    Object? sportOfChoice = freezed,
    Object? specificSportActivity = freezed,
    Object? physicalLimitations = null,
    Object? healthConditions = null,
    Object? exercisesToAvoid = null,
    Object? excludedExercises = null,
    Object? additionalHealthInfo = freezed,
    Object? dietPreferences = null,
    Object? dietaryRestrictions = null,
    Object? mealsPerDay = freezed,
    Object? caloricGoal = freezed,
    Object? takingSupplements = freezed,
    Object? supplements = null,
    Object? sleepQuality = freezed,
    Object? onboardingCompleted = null,
    Object? hasCompletedPreferences = null,
    Object? fitnessAssessmentCompleted = null,
    Object? fitnessGuide = freezed,
    Object? additionalNotes = freezed,
    Object? metadata = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      displayName: freezed == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      gender: freezed == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
      age: freezed == age
          ? _value.age
          : age // ignore: cast_nullable_to_non_nullable
              as int?,
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double?,
      heightUnit: null == heightUnit
          ? _value.heightUnit
          : heightUnit // ignore: cast_nullable_to_non_nullable
              as String,
      weight: freezed == weight
          ? _value.weight
          : weight // ignore: cast_nullable_to_non_nullable
              as double?,
      weightUnit: null == weightUnit
          ? _value.weightUnit
          : weightUnit // ignore: cast_nullable_to_non_nullable
              as String,
      fitnessGoalPrimary: freezed == fitnessGoalPrimary
          ? _value.fitnessGoalPrimary
          : fitnessGoalPrimary // ignore: cast_nullable_to_non_nullable
              as String?,
      fitnessGoalsArray: null == fitnessGoalsArray
          ? _value.fitnessGoalsArray
          : fitnessGoalsArray // ignore: cast_nullable_to_non_nullable
              as List<String>,
      fitnessGoalsOrder: null == fitnessGoalsOrder
          ? _value.fitnessGoalsOrder
          : fitnessGoalsOrder // ignore: cast_nullable_to_non_nullable
              as List<String>,
      trainingExperienceLevel: freezed == trainingExperienceLevel
          ? _value.trainingExperienceLevel
          : trainingExperienceLevel // ignore: cast_nullable_to_non_nullable
              as String?,
      fitnessExperience: freezed == fitnessExperience
          ? _value.fitnessExperience
          : fitnessExperience // ignore: cast_nullable_to_non_nullable
              as String?,
      fitnessLevel: freezed == fitnessLevel
          ? _value.fitnessLevel
          : fitnessLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      cardioFitnessLevel: freezed == cardioFitnessLevel
          ? _value.cardioFitnessLevel
          : cardioFitnessLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      weightliftingFitnessLevel: freezed == weightliftingFitnessLevel
          ? _value.weightliftingFitnessLevel
          : weightliftingFitnessLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      cardioLevel: freezed == cardioLevel
          ? _value.cardioLevel
          : cardioLevel // ignore: cast_nullable_to_non_nullable
              as String?,
      cardioLevelDescription: freezed == cardioLevelDescription
          ? _value.cardioLevelDescription
          : cardioLevelDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      weightliftingLevel: freezed == weightliftingLevel
          ? _value.weightliftingLevel
          : weightliftingLevel // ignore: cast_nullable_to_non_nullable
              as String?,
      weightliftingLevelDescription: freezed == weightliftingLevelDescription
          ? _value.weightliftingLevelDescription
          : weightliftingLevelDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      workoutFrequencyDays: freezed == workoutFrequencyDays
          ? _value.workoutFrequencyDays
          : workoutFrequencyDays // ignore: cast_nullable_to_non_nullable
              as int?,
      preferredWorkoutDaysCount: freezed == preferredWorkoutDaysCount
          ? _value.preferredWorkoutDaysCount
          : preferredWorkoutDaysCount // ignore: cast_nullable_to_non_nullable
              as int?,
      workoutDays: null == workoutDays
          ? _value.workoutDays
          : workoutDays // ignore: cast_nullable_to_non_nullable
              as List<String>,
      workoutDurationPreference: freezed == workoutDurationPreference
          ? _value.workoutDurationPreference
          : workoutDurationPreference // ignore: cast_nullable_to_non_nullable
              as String?,
      preferredWorkoutDuration: freezed == preferredWorkoutDuration
          ? _value.preferredWorkoutDuration
          : preferredWorkoutDuration // ignore: cast_nullable_to_non_nullable
              as String?,
      exercisePreferences: null == exercisePreferences
          ? _value.exercisePreferences
          : exercisePreferences // ignore: cast_nullable_to_non_nullable
              as List<String>,
      equipment: null == equipment
          ? _value.equipment
          : equipment // ignore: cast_nullable_to_non_nullable
              as List<String>,
      workoutEnvironment: null == workoutEnvironment
          ? _value.workoutEnvironment
          : workoutEnvironment // ignore: cast_nullable_to_non_nullable
              as List<String>,
      sportActivity: freezed == sportActivity
          ? _value.sportActivity
          : sportActivity // ignore: cast_nullable_to_non_nullable
              as String?,
      sportOfChoice: freezed == sportOfChoice
          ? _value.sportOfChoice
          : sportOfChoice // ignore: cast_nullable_to_non_nullable
              as String?,
      specificSportActivity: freezed == specificSportActivity
          ? _value.specificSportActivity
          : specificSportActivity // ignore: cast_nullable_to_non_nullable
              as String?,
      physicalLimitations: null == physicalLimitations
          ? _value.physicalLimitations
          : physicalLimitations // ignore: cast_nullable_to_non_nullable
              as List<String>,
      healthConditions: null == healthConditions
          ? _value.healthConditions
          : healthConditions // ignore: cast_nullable_to_non_nullable
              as List<String>,
      exercisesToAvoid: null == exercisesToAvoid
          ? _value.exercisesToAvoid
          : exercisesToAvoid // ignore: cast_nullable_to_non_nullable
              as List<String>,
      excludedExercises: null == excludedExercises
          ? _value.excludedExercises
          : excludedExercises // ignore: cast_nullable_to_non_nullable
              as List<String>,
      additionalHealthInfo: freezed == additionalHealthInfo
          ? _value.additionalHealthInfo
          : additionalHealthInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      dietPreferences: null == dietPreferences
          ? _value.dietPreferences
          : dietPreferences // ignore: cast_nullable_to_non_nullable
              as List<String>,
      dietaryRestrictions: null == dietaryRestrictions
          ? _value.dietaryRestrictions
          : dietaryRestrictions // ignore: cast_nullable_to_non_nullable
              as List<String>,
      mealsPerDay: freezed == mealsPerDay
          ? _value.mealsPerDay
          : mealsPerDay // ignore: cast_nullable_to_non_nullable
              as int?,
      caloricGoal: freezed == caloricGoal
          ? _value.caloricGoal
          : caloricGoal // ignore: cast_nullable_to_non_nullable
              as int?,
      takingSupplements: freezed == takingSupplements
          ? _value.takingSupplements
          : takingSupplements // ignore: cast_nullable_to_non_nullable
              as bool?,
      supplements: null == supplements
          ? _value.supplements
          : supplements // ignore: cast_nullable_to_non_nullable
              as List<String>,
      sleepQuality: freezed == sleepQuality
          ? _value.sleepQuality
          : sleepQuality // ignore: cast_nullable_to_non_nullable
              as String?,
      onboardingCompleted: null == onboardingCompleted
          ? _value.onboardingCompleted
          : onboardingCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      hasCompletedPreferences: null == hasCompletedPreferences
          ? _value.hasCompletedPreferences
          : hasCompletedPreferences // ignore: cast_nullable_to_non_nullable
              as bool,
      fitnessAssessmentCompleted: null == fitnessAssessmentCompleted
          ? _value.fitnessAssessmentCompleted
          : fitnessAssessmentCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      fitnessGuide: freezed == fitnessGuide
          ? _value.fitnessGuide
          : fitnessGuide // ignore: cast_nullable_to_non_nullable
              as String?,
      additionalNotes: freezed == additionalNotes
          ? _value.additionalNotes
          : additionalNotes // ignore: cast_nullable_to_non_nullable
              as String?,
      metadata: freezed == metadata
          ? _value.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserProfileImplCopyWith<$Res>
    implements $UserProfileCopyWith<$Res> {
  factory _$$UserProfileImplCopyWith(
          _$UserProfileImpl value, $Res Function(_$UserProfileImpl) then) =
      __$$UserProfileImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String id,
      String? email,
      String? displayName,
      DateTime createdAt,
      DateTime updatedAt,
      String? gender,
      int? age,
      double? height,
      String heightUnit,
      double? weight,
      String weightUnit,
      String? fitnessGoalPrimary,
      List<String> fitnessGoalsArray,
      List<String> fitnessGoalsOrder,
      String? trainingExperienceLevel,
      String? fitnessExperience,
      int? fitnessLevel,
      int? cardioFitnessLevel,
      int? weightliftingFitnessLevel,
      String? cardioLevel,
      String? cardioLevelDescription,
      String? weightliftingLevel,
      String? weightliftingLevelDescription,
      int? workoutFrequencyDays,
      int? preferredWorkoutDaysCount,
      List<String> workoutDays,
      String? workoutDurationPreference,
      String? preferredWorkoutDuration,
      List<String> exercisePreferences,
      List<String> equipment,
      List<String> workoutEnvironment,
      String? sportActivity,
      String? sportOfChoice,
      String? specificSportActivity,
      List<String> physicalLimitations,
      List<String> healthConditions,
      List<String> exercisesToAvoid,
      List<String> excludedExercises,
      String? additionalHealthInfo,
      List<String> dietPreferences,
      List<String> dietaryRestrictions,
      int? mealsPerDay,
      int? caloricGoal,
      bool? takingSupplements,
      List<String> supplements,
      String? sleepQuality,
      bool onboardingCompleted,
      bool hasCompletedPreferences,
      bool fitnessAssessmentCompleted,
      String? fitnessGuide,
      String? additionalNotes,
      Map<String, dynamic>? metadata});
}

/// @nodoc
class __$$UserProfileImplCopyWithImpl<$Res>
    extends _$UserProfileCopyWithImpl<$Res, _$UserProfileImpl>
    implements _$$UserProfileImplCopyWith<$Res> {
  __$$UserProfileImplCopyWithImpl(
      _$UserProfileImpl _value, $Res Function(_$UserProfileImpl) _then)
      : super(_value, _then);

  /// Create a copy of UserProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? email = freezed,
    Object? displayName = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? gender = freezed,
    Object? age = freezed,
    Object? height = freezed,
    Object? heightUnit = null,
    Object? weight = freezed,
    Object? weightUnit = null,
    Object? fitnessGoalPrimary = freezed,
    Object? fitnessGoalsArray = null,
    Object? fitnessGoalsOrder = null,
    Object? trainingExperienceLevel = freezed,
    Object? fitnessExperience = freezed,
    Object? fitnessLevel = freezed,
    Object? cardioFitnessLevel = freezed,
    Object? weightliftingFitnessLevel = freezed,
    Object? cardioLevel = freezed,
    Object? cardioLevelDescription = freezed,
    Object? weightliftingLevel = freezed,
    Object? weightliftingLevelDescription = freezed,
    Object? workoutFrequencyDays = freezed,
    Object? preferredWorkoutDaysCount = freezed,
    Object? workoutDays = null,
    Object? workoutDurationPreference = freezed,
    Object? preferredWorkoutDuration = freezed,
    Object? exercisePreferences = null,
    Object? equipment = null,
    Object? workoutEnvironment = null,
    Object? sportActivity = freezed,
    Object? sportOfChoice = freezed,
    Object? specificSportActivity = freezed,
    Object? physicalLimitations = null,
    Object? healthConditions = null,
    Object? exercisesToAvoid = null,
    Object? excludedExercises = null,
    Object? additionalHealthInfo = freezed,
    Object? dietPreferences = null,
    Object? dietaryRestrictions = null,
    Object? mealsPerDay = freezed,
    Object? caloricGoal = freezed,
    Object? takingSupplements = freezed,
    Object? supplements = null,
    Object? sleepQuality = freezed,
    Object? onboardingCompleted = null,
    Object? hasCompletedPreferences = null,
    Object? fitnessAssessmentCompleted = null,
    Object? fitnessGuide = freezed,
    Object? additionalNotes = freezed,
    Object? metadata = freezed,
  }) {
    return _then(_$UserProfileImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      displayName: freezed == displayName
          ? _value.displayName
          : displayName // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      gender: freezed == gender
          ? _value.gender
          : gender // ignore: cast_nullable_to_non_nullable
              as String?,
      age: freezed == age
          ? _value.age
          : age // ignore: cast_nullable_to_non_nullable
              as int?,
      height: freezed == height
          ? _value.height
          : height // ignore: cast_nullable_to_non_nullable
              as double?,
      heightUnit: null == heightUnit
          ? _value.heightUnit
          : heightUnit // ignore: cast_nullable_to_non_nullable
              as String,
      weight: freezed == weight
          ? _value.weight
          : weight // ignore: cast_nullable_to_non_nullable
              as double?,
      weightUnit: null == weightUnit
          ? _value.weightUnit
          : weightUnit // ignore: cast_nullable_to_non_nullable
              as String,
      fitnessGoalPrimary: freezed == fitnessGoalPrimary
          ? _value.fitnessGoalPrimary
          : fitnessGoalPrimary // ignore: cast_nullable_to_non_nullable
              as String?,
      fitnessGoalsArray: null == fitnessGoalsArray
          ? _value._fitnessGoalsArray
          : fitnessGoalsArray // ignore: cast_nullable_to_non_nullable
              as List<String>,
      fitnessGoalsOrder: null == fitnessGoalsOrder
          ? _value._fitnessGoalsOrder
          : fitnessGoalsOrder // ignore: cast_nullable_to_non_nullable
              as List<String>,
      trainingExperienceLevel: freezed == trainingExperienceLevel
          ? _value.trainingExperienceLevel
          : trainingExperienceLevel // ignore: cast_nullable_to_non_nullable
              as String?,
      fitnessExperience: freezed == fitnessExperience
          ? _value.fitnessExperience
          : fitnessExperience // ignore: cast_nullable_to_non_nullable
              as String?,
      fitnessLevel: freezed == fitnessLevel
          ? _value.fitnessLevel
          : fitnessLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      cardioFitnessLevel: freezed == cardioFitnessLevel
          ? _value.cardioFitnessLevel
          : cardioFitnessLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      weightliftingFitnessLevel: freezed == weightliftingFitnessLevel
          ? _value.weightliftingFitnessLevel
          : weightliftingFitnessLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      cardioLevel: freezed == cardioLevel
          ? _value.cardioLevel
          : cardioLevel // ignore: cast_nullable_to_non_nullable
              as String?,
      cardioLevelDescription: freezed == cardioLevelDescription
          ? _value.cardioLevelDescription
          : cardioLevelDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      weightliftingLevel: freezed == weightliftingLevel
          ? _value.weightliftingLevel
          : weightliftingLevel // ignore: cast_nullable_to_non_nullable
              as String?,
      weightliftingLevelDescription: freezed == weightliftingLevelDescription
          ? _value.weightliftingLevelDescription
          : weightliftingLevelDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      workoutFrequencyDays: freezed == workoutFrequencyDays
          ? _value.workoutFrequencyDays
          : workoutFrequencyDays // ignore: cast_nullable_to_non_nullable
              as int?,
      preferredWorkoutDaysCount: freezed == preferredWorkoutDaysCount
          ? _value.preferredWorkoutDaysCount
          : preferredWorkoutDaysCount // ignore: cast_nullable_to_non_nullable
              as int?,
      workoutDays: null == workoutDays
          ? _value._workoutDays
          : workoutDays // ignore: cast_nullable_to_non_nullable
              as List<String>,
      workoutDurationPreference: freezed == workoutDurationPreference
          ? _value.workoutDurationPreference
          : workoutDurationPreference // ignore: cast_nullable_to_non_nullable
              as String?,
      preferredWorkoutDuration: freezed == preferredWorkoutDuration
          ? _value.preferredWorkoutDuration
          : preferredWorkoutDuration // ignore: cast_nullable_to_non_nullable
              as String?,
      exercisePreferences: null == exercisePreferences
          ? _value._exercisePreferences
          : exercisePreferences // ignore: cast_nullable_to_non_nullable
              as List<String>,
      equipment: null == equipment
          ? _value._equipment
          : equipment // ignore: cast_nullable_to_non_nullable
              as List<String>,
      workoutEnvironment: null == workoutEnvironment
          ? _value._workoutEnvironment
          : workoutEnvironment // ignore: cast_nullable_to_non_nullable
              as List<String>,
      sportActivity: freezed == sportActivity
          ? _value.sportActivity
          : sportActivity // ignore: cast_nullable_to_non_nullable
              as String?,
      sportOfChoice: freezed == sportOfChoice
          ? _value.sportOfChoice
          : sportOfChoice // ignore: cast_nullable_to_non_nullable
              as String?,
      specificSportActivity: freezed == specificSportActivity
          ? _value.specificSportActivity
          : specificSportActivity // ignore: cast_nullable_to_non_nullable
              as String?,
      physicalLimitations: null == physicalLimitations
          ? _value._physicalLimitations
          : physicalLimitations // ignore: cast_nullable_to_non_nullable
              as List<String>,
      healthConditions: null == healthConditions
          ? _value._healthConditions
          : healthConditions // ignore: cast_nullable_to_non_nullable
              as List<String>,
      exercisesToAvoid: null == exercisesToAvoid
          ? _value._exercisesToAvoid
          : exercisesToAvoid // ignore: cast_nullable_to_non_nullable
              as List<String>,
      excludedExercises: null == excludedExercises
          ? _value._excludedExercises
          : excludedExercises // ignore: cast_nullable_to_non_nullable
              as List<String>,
      additionalHealthInfo: freezed == additionalHealthInfo
          ? _value.additionalHealthInfo
          : additionalHealthInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      dietPreferences: null == dietPreferences
          ? _value._dietPreferences
          : dietPreferences // ignore: cast_nullable_to_non_nullable
              as List<String>,
      dietaryRestrictions: null == dietaryRestrictions
          ? _value._dietaryRestrictions
          : dietaryRestrictions // ignore: cast_nullable_to_non_nullable
              as List<String>,
      mealsPerDay: freezed == mealsPerDay
          ? _value.mealsPerDay
          : mealsPerDay // ignore: cast_nullable_to_non_nullable
              as int?,
      caloricGoal: freezed == caloricGoal
          ? _value.caloricGoal
          : caloricGoal // ignore: cast_nullable_to_non_nullable
              as int?,
      takingSupplements: freezed == takingSupplements
          ? _value.takingSupplements
          : takingSupplements // ignore: cast_nullable_to_non_nullable
              as bool?,
      supplements: null == supplements
          ? _value._supplements
          : supplements // ignore: cast_nullable_to_non_nullable
              as List<String>,
      sleepQuality: freezed == sleepQuality
          ? _value.sleepQuality
          : sleepQuality // ignore: cast_nullable_to_non_nullable
              as String?,
      onboardingCompleted: null == onboardingCompleted
          ? _value.onboardingCompleted
          : onboardingCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      hasCompletedPreferences: null == hasCompletedPreferences
          ? _value.hasCompletedPreferences
          : hasCompletedPreferences // ignore: cast_nullable_to_non_nullable
              as bool,
      fitnessAssessmentCompleted: null == fitnessAssessmentCompleted
          ? _value.fitnessAssessmentCompleted
          : fitnessAssessmentCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      fitnessGuide: freezed == fitnessGuide
          ? _value.fitnessGuide
          : fitnessGuide // ignore: cast_nullable_to_non_nullable
              as String?,
      additionalNotes: freezed == additionalNotes
          ? _value.additionalNotes
          : additionalNotes // ignore: cast_nullable_to_non_nullable
              as String?,
      metadata: freezed == metadata
          ? _value._metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserProfileImpl implements _UserProfile {
  const _$UserProfileImpl(
      {required this.id,
      this.email,
      this.displayName,
      required this.createdAt,
      required this.updatedAt,
      this.gender,
      this.age,
      this.height,
      this.heightUnit = 'cm',
      this.weight,
      this.weightUnit = 'kg',
      this.fitnessGoalPrimary,
      final List<String> fitnessGoalsArray = const [],
      final List<String> fitnessGoalsOrder = const [],
      this.trainingExperienceLevel,
      this.fitnessExperience,
      this.fitnessLevel,
      this.cardioFitnessLevel,
      this.weightliftingFitnessLevel,
      this.cardioLevel,
      this.cardioLevelDescription,
      this.weightliftingLevel,
      this.weightliftingLevelDescription,
      this.workoutFrequencyDays,
      this.preferredWorkoutDaysCount,
      final List<String> workoutDays = const [],
      this.workoutDurationPreference,
      this.preferredWorkoutDuration,
      final List<String> exercisePreferences = const [],
      final List<String> equipment = const [],
      final List<String> workoutEnvironment = const [],
      this.sportActivity,
      this.sportOfChoice,
      this.specificSportActivity,
      final List<String> physicalLimitations = const [],
      final List<String> healthConditions = const [],
      final List<String> exercisesToAvoid = const [],
      final List<String> excludedExercises = const [],
      this.additionalHealthInfo,
      final List<String> dietPreferences = const [],
      final List<String> dietaryRestrictions = const [],
      this.mealsPerDay,
      this.caloricGoal,
      this.takingSupplements,
      final List<String> supplements = const [],
      this.sleepQuality,
      this.onboardingCompleted = false,
      this.hasCompletedPreferences = false,
      this.fitnessAssessmentCompleted = false,
      this.fitnessGuide,
      this.additionalNotes,
      final Map<String, dynamic>? metadata})
      : _fitnessGoalsArray = fitnessGoalsArray,
        _fitnessGoalsOrder = fitnessGoalsOrder,
        _workoutDays = workoutDays,
        _exercisePreferences = exercisePreferences,
        _equipment = equipment,
        _workoutEnvironment = workoutEnvironment,
        _physicalLimitations = physicalLimitations,
        _healthConditions = healthConditions,
        _exercisesToAvoid = exercisesToAvoid,
        _excludedExercises = excludedExercises,
        _dietPreferences = dietPreferences,
        _dietaryRestrictions = dietaryRestrictions,
        _supplements = supplements,
        _metadata = metadata;

  factory _$UserProfileImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserProfileImplFromJson(json);

  @override
  final String id;
  @override
  final String? email;
  @override
  final String? displayName;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
// Personal Information
  @override
  final String? gender;
  @override
  final int? age;
  @override
  final double? height;
  @override
  @JsonKey()
  final String heightUnit;
  @override
  final double? weight;
  @override
  @JsonKey()
  final String weightUnit;
// Fitness Goals
  @override
  final String? fitnessGoalPrimary;
  final List<String> _fitnessGoalsArray;
  @override
  @JsonKey()
  List<String> get fitnessGoalsArray {
    if (_fitnessGoalsArray is EqualUnmodifiableListView)
      return _fitnessGoalsArray;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_fitnessGoalsArray);
  }

  final List<String> _fitnessGoalsOrder;
  @override
  @JsonKey()
  List<String> get fitnessGoalsOrder {
    if (_fitnessGoalsOrder is EqualUnmodifiableListView)
      return _fitnessGoalsOrder;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_fitnessGoalsOrder);
  }

// Fitness Experience & Levels
  @override
  final String? trainingExperienceLevel;
  @override
  final String? fitnessExperience;
  @override
  final int? fitnessLevel;
  @override
  final int? cardioFitnessLevel;
  @override
  final int? weightliftingFitnessLevel;
  @override
  final String? cardioLevel;
  @override
  final String? cardioLevelDescription;
  @override
  final String? weightliftingLevel;
  @override
  final String? weightliftingLevelDescription;
// Workout Preferences
  @override
  final int? workoutFrequencyDays;
  @override
  final int? preferredWorkoutDaysCount;
  final List<String> _workoutDays;
  @override
  @JsonKey()
  List<String> get workoutDays {
    if (_workoutDays is EqualUnmodifiableListView) return _workoutDays;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_workoutDays);
  }

  @override
  final String? workoutDurationPreference;
  @override
  final String? preferredWorkoutDuration;
// Exercise & Equipment
  final List<String> _exercisePreferences;
// Exercise & Equipment
  @override
  @JsonKey()
  List<String> get exercisePreferences {
    if (_exercisePreferences is EqualUnmodifiableListView)
      return _exercisePreferences;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_exercisePreferences);
  }

  final List<String> _equipment;
  @override
  @JsonKey()
  List<String> get equipment {
    if (_equipment is EqualUnmodifiableListView) return _equipment;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_equipment);
  }

  final List<String> _workoutEnvironment;
  @override
  @JsonKey()
  List<String> get workoutEnvironment {
    if (_workoutEnvironment is EqualUnmodifiableListView)
      return _workoutEnvironment;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_workoutEnvironment);
  }

  @override
  final String? sportActivity;
  @override
  final String? sportOfChoice;
  @override
  final String? specificSportActivity;
// Limitations & Restrictions
  final List<String> _physicalLimitations;
// Limitations & Restrictions
  @override
  @JsonKey()
  List<String> get physicalLimitations {
    if (_physicalLimitations is EqualUnmodifiableListView)
      return _physicalLimitations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_physicalLimitations);
  }

  final List<String> _healthConditions;
  @override
  @JsonKey()
  List<String> get healthConditions {
    if (_healthConditions is EqualUnmodifiableListView)
      return _healthConditions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_healthConditions);
  }

  final List<String> _exercisesToAvoid;
  @override
  @JsonKey()
  List<String> get exercisesToAvoid {
    if (_exercisesToAvoid is EqualUnmodifiableListView)
      return _exercisesToAvoid;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_exercisesToAvoid);
  }

  final List<String> _excludedExercises;
  @override
  @JsonKey()
  List<String> get excludedExercises {
    if (_excludedExercises is EqualUnmodifiableListView)
      return _excludedExercises;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_excludedExercises);
  }

  @override
  final String? additionalHealthInfo;
// Nutrition & Lifestyle
  final List<String> _dietPreferences;
// Nutrition & Lifestyle
  @override
  @JsonKey()
  List<String> get dietPreferences {
    if (_dietPreferences is EqualUnmodifiableListView) return _dietPreferences;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_dietPreferences);
  }

  final List<String> _dietaryRestrictions;
  @override
  @JsonKey()
  List<String> get dietaryRestrictions {
    if (_dietaryRestrictions is EqualUnmodifiableListView)
      return _dietaryRestrictions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_dietaryRestrictions);
  }

  @override
  final int? mealsPerDay;
  @override
  final int? caloricGoal;
  @override
  final bool? takingSupplements;
  final List<String> _supplements;
  @override
  @JsonKey()
  List<String> get supplements {
    if (_supplements is EqualUnmodifiableListView) return _supplements;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_supplements);
  }

  @override
  final String? sleepQuality;
// Progress Tracking
  @override
  @JsonKey()
  final bool onboardingCompleted;
  @override
  @JsonKey()
  final bool hasCompletedPreferences;
  @override
  @JsonKey()
  final bool fitnessAssessmentCompleted;
// Additional Data
  @override
  final String? fitnessGuide;
  @override
  final String? additionalNotes;
  final Map<String, dynamic>? _metadata;
  @override
  Map<String, dynamic>? get metadata {
    final value = _metadata;
    if (value == null) return null;
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  String toString() {
    return 'UserProfile(id: $id, email: $email, displayName: $displayName, createdAt: $createdAt, updatedAt: $updatedAt, gender: $gender, age: $age, height: $height, heightUnit: $heightUnit, weight: $weight, weightUnit: $weightUnit, fitnessGoalPrimary: $fitnessGoalPrimary, fitnessGoalsArray: $fitnessGoalsArray, fitnessGoalsOrder: $fitnessGoalsOrder, trainingExperienceLevel: $trainingExperienceLevel, fitnessExperience: $fitnessExperience, fitnessLevel: $fitnessLevel, cardioFitnessLevel: $cardioFitnessLevel, weightliftingFitnessLevel: $weightliftingFitnessLevel, cardioLevel: $cardioLevel, cardioLevelDescription: $cardioLevelDescription, weightliftingLevel: $weightliftingLevel, weightliftingLevelDescription: $weightliftingLevelDescription, workoutFrequencyDays: $workoutFrequencyDays, preferredWorkoutDaysCount: $preferredWorkoutDaysCount, workoutDays: $workoutDays, workoutDurationPreference: $workoutDurationPreference, preferredWorkoutDuration: $preferredWorkoutDuration, exercisePreferences: $exercisePreferences, equipment: $equipment, workoutEnvironment: $workoutEnvironment, sportActivity: $sportActivity, sportOfChoice: $sportOfChoice, specificSportActivity: $specificSportActivity, physicalLimitations: $physicalLimitations, healthConditions: $healthConditions, exercisesToAvoid: $exercisesToAvoid, excludedExercises: $excludedExercises, additionalHealthInfo: $additionalHealthInfo, dietPreferences: $dietPreferences, dietaryRestrictions: $dietaryRestrictions, mealsPerDay: $mealsPerDay, caloricGoal: $caloricGoal, takingSupplements: $takingSupplements, supplements: $supplements, sleepQuality: $sleepQuality, onboardingCompleted: $onboardingCompleted, hasCompletedPreferences: $hasCompletedPreferences, fitnessAssessmentCompleted: $fitnessAssessmentCompleted, fitnessGuide: $fitnessGuide, additionalNotes: $additionalNotes, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserProfileImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.displayName, displayName) ||
                other.displayName == displayName) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.gender, gender) || other.gender == gender) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.heightUnit, heightUnit) ||
                other.heightUnit == heightUnit) &&
            (identical(other.weight, weight) || other.weight == weight) &&
            (identical(other.weightUnit, weightUnit) ||
                other.weightUnit == weightUnit) &&
            (identical(other.fitnessGoalPrimary, fitnessGoalPrimary) ||
                other.fitnessGoalPrimary == fitnessGoalPrimary) &&
            const DeepCollectionEquality()
                .equals(other._fitnessGoalsArray, _fitnessGoalsArray) &&
            const DeepCollectionEquality()
                .equals(other._fitnessGoalsOrder, _fitnessGoalsOrder) &&
            (identical(other.trainingExperienceLevel, trainingExperienceLevel) ||
                other.trainingExperienceLevel == trainingExperienceLevel) &&
            (identical(other.fitnessExperience, fitnessExperience) ||
                other.fitnessExperience == fitnessExperience) &&
            (identical(other.fitnessLevel, fitnessLevel) ||
                other.fitnessLevel == fitnessLevel) &&
            (identical(other.cardioFitnessLevel, cardioFitnessLevel) ||
                other.cardioFitnessLevel == cardioFitnessLevel) &&
            (identical(other.weightliftingFitnessLevel, weightliftingFitnessLevel) ||
                other.weightliftingFitnessLevel == weightliftingFitnessLevel) &&
            (identical(other.cardioLevel, cardioLevel) ||
                other.cardioLevel == cardioLevel) &&
            (identical(other.cardioLevelDescription, cardioLevelDescription) ||
                other.cardioLevelDescription == cardioLevelDescription) &&
            (identical(other.weightliftingLevel, weightliftingLevel) ||
                other.weightliftingLevel == weightliftingLevel) &&
            (identical(other.weightliftingLevelDescription, weightliftingLevelDescription) ||
                other.weightliftingLevelDescription ==
                    weightliftingLevelDescription) &&
            (identical(other.workoutFrequencyDays, workoutFrequencyDays) ||
                other.workoutFrequencyDays == workoutFrequencyDays) &&
            (identical(other.preferredWorkoutDaysCount, preferredWorkoutDaysCount) ||
                other.preferredWorkoutDaysCount == preferredWorkoutDaysCount) &&
            const DeepCollectionEquality()
                .equals(other._workoutDays, _workoutDays) &&
            (identical(other.workoutDurationPreference, workoutDurationPreference) ||
                other.workoutDurationPreference == workoutDurationPreference) &&
            (identical(other.preferredWorkoutDuration, preferredWorkoutDuration) ||
                other.preferredWorkoutDuration == preferredWorkoutDuration) &&
            const DeepCollectionEquality()
                .equals(other._exercisePreferences, _exercisePreferences) &&
            const DeepCollectionEquality()
                .equals(other._equipment, _equipment) &&
            const DeepCollectionEquality()
                .equals(other._workoutEnvironment, _workoutEnvironment) &&
            (identical(other.sportActivity, sportActivity) ||
                other.sportActivity == sportActivity) &&
            (identical(other.sportOfChoice, sportOfChoice) ||
                other.sportOfChoice == sportOfChoice) &&
            (identical(other.specificSportActivity, specificSportActivity) ||
                other.specificSportActivity == specificSportActivity) &&
            const DeepCollectionEquality().equals(other._physicalLimitations, _physicalLimitations) &&
            const DeepCollectionEquality().equals(other._healthConditions, _healthConditions) &&
            const DeepCollectionEquality().equals(other._exercisesToAvoid, _exercisesToAvoid) &&
            const DeepCollectionEquality().equals(other._excludedExercises, _excludedExercises) &&
            (identical(other.additionalHealthInfo, additionalHealthInfo) || other.additionalHealthInfo == additionalHealthInfo) &&
            const DeepCollectionEquality().equals(other._dietPreferences, _dietPreferences) &&
            const DeepCollectionEquality().equals(other._dietaryRestrictions, _dietaryRestrictions) &&
            (identical(other.mealsPerDay, mealsPerDay) || other.mealsPerDay == mealsPerDay) &&
            (identical(other.caloricGoal, caloricGoal) || other.caloricGoal == caloricGoal) &&
            (identical(other.takingSupplements, takingSupplements) || other.takingSupplements == takingSupplements) &&
            const DeepCollectionEquality().equals(other._supplements, _supplements) &&
            (identical(other.sleepQuality, sleepQuality) || other.sleepQuality == sleepQuality) &&
            (identical(other.onboardingCompleted, onboardingCompleted) || other.onboardingCompleted == onboardingCompleted) &&
            (identical(other.hasCompletedPreferences, hasCompletedPreferences) || other.hasCompletedPreferences == hasCompletedPreferences) &&
            (identical(other.fitnessAssessmentCompleted, fitnessAssessmentCompleted) || other.fitnessAssessmentCompleted == fitnessAssessmentCompleted) &&
            (identical(other.fitnessGuide, fitnessGuide) || other.fitnessGuide == fitnessGuide) &&
            (identical(other.additionalNotes, additionalNotes) || other.additionalNotes == additionalNotes) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        email,
        displayName,
        createdAt,
        updatedAt,
        gender,
        age,
        height,
        heightUnit,
        weight,
        weightUnit,
        fitnessGoalPrimary,
        const DeepCollectionEquality().hash(_fitnessGoalsArray),
        const DeepCollectionEquality().hash(_fitnessGoalsOrder),
        trainingExperienceLevel,
        fitnessExperience,
        fitnessLevel,
        cardioFitnessLevel,
        weightliftingFitnessLevel,
        cardioLevel,
        cardioLevelDescription,
        weightliftingLevel,
        weightliftingLevelDescription,
        workoutFrequencyDays,
        preferredWorkoutDaysCount,
        const DeepCollectionEquality().hash(_workoutDays),
        workoutDurationPreference,
        preferredWorkoutDuration,
        const DeepCollectionEquality().hash(_exercisePreferences),
        const DeepCollectionEquality().hash(_equipment),
        const DeepCollectionEquality().hash(_workoutEnvironment),
        sportActivity,
        sportOfChoice,
        specificSportActivity,
        const DeepCollectionEquality().hash(_physicalLimitations),
        const DeepCollectionEquality().hash(_healthConditions),
        const DeepCollectionEquality().hash(_exercisesToAvoid),
        const DeepCollectionEquality().hash(_excludedExercises),
        additionalHealthInfo,
        const DeepCollectionEquality().hash(_dietPreferences),
        const DeepCollectionEquality().hash(_dietaryRestrictions),
        mealsPerDay,
        caloricGoal,
        takingSupplements,
        const DeepCollectionEquality().hash(_supplements),
        sleepQuality,
        onboardingCompleted,
        hasCompletedPreferences,
        fitnessAssessmentCompleted,
        fitnessGuide,
        additionalNotes,
        const DeepCollectionEquality().hash(_metadata)
      ]);

  /// Create a copy of UserProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UserProfileImplCopyWith<_$UserProfileImpl> get copyWith =>
      __$$UserProfileImplCopyWithImpl<_$UserProfileImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserProfileImplToJson(
      this,
    );
  }
}

abstract class _UserProfile implements UserProfile {
  const factory _UserProfile(
      {required final String id,
      final String? email,
      final String? displayName,
      required final DateTime createdAt,
      required final DateTime updatedAt,
      final String? gender,
      final int? age,
      final double? height,
      final String heightUnit,
      final double? weight,
      final String weightUnit,
      final String? fitnessGoalPrimary,
      final List<String> fitnessGoalsArray,
      final List<String> fitnessGoalsOrder,
      final String? trainingExperienceLevel,
      final String? fitnessExperience,
      final int? fitnessLevel,
      final int? cardioFitnessLevel,
      final int? weightliftingFitnessLevel,
      final String? cardioLevel,
      final String? cardioLevelDescription,
      final String? weightliftingLevel,
      final String? weightliftingLevelDescription,
      final int? workoutFrequencyDays,
      final int? preferredWorkoutDaysCount,
      final List<String> workoutDays,
      final String? workoutDurationPreference,
      final String? preferredWorkoutDuration,
      final List<String> exercisePreferences,
      final List<String> equipment,
      final List<String> workoutEnvironment,
      final String? sportActivity,
      final String? sportOfChoice,
      final String? specificSportActivity,
      final List<String> physicalLimitations,
      final List<String> healthConditions,
      final List<String> exercisesToAvoid,
      final List<String> excludedExercises,
      final String? additionalHealthInfo,
      final List<String> dietPreferences,
      final List<String> dietaryRestrictions,
      final int? mealsPerDay,
      final int? caloricGoal,
      final bool? takingSupplements,
      final List<String> supplements,
      final String? sleepQuality,
      final bool onboardingCompleted,
      final bool hasCompletedPreferences,
      final bool fitnessAssessmentCompleted,
      final String? fitnessGuide,
      final String? additionalNotes,
      final Map<String, dynamic>? metadata}) = _$UserProfileImpl;

  factory _UserProfile.fromJson(Map<String, dynamic> json) =
      _$UserProfileImpl.fromJson;

  @override
  String get id;
  @override
  String? get email;
  @override
  String? get displayName;
  @override
  DateTime get createdAt;
  @override
  DateTime get updatedAt; // Personal Information
  @override
  String? get gender;
  @override
  int? get age;
  @override
  double? get height;
  @override
  String get heightUnit;
  @override
  double? get weight;
  @override
  String get weightUnit; // Fitness Goals
  @override
  String? get fitnessGoalPrimary;
  @override
  List<String> get fitnessGoalsArray;
  @override
  List<String> get fitnessGoalsOrder; // Fitness Experience & Levels
  @override
  String? get trainingExperienceLevel;
  @override
  String? get fitnessExperience;
  @override
  int? get fitnessLevel;
  @override
  int? get cardioFitnessLevel;
  @override
  int? get weightliftingFitnessLevel;
  @override
  String? get cardioLevel;
  @override
  String? get cardioLevelDescription;
  @override
  String? get weightliftingLevel;
  @override
  String? get weightliftingLevelDescription; // Workout Preferences
  @override
  int? get workoutFrequencyDays;
  @override
  int? get preferredWorkoutDaysCount;
  @override
  List<String> get workoutDays;
  @override
  String? get workoutDurationPreference;
  @override
  String? get preferredWorkoutDuration; // Exercise & Equipment
  @override
  List<String> get exercisePreferences;
  @override
  List<String> get equipment;
  @override
  List<String> get workoutEnvironment;
  @override
  String? get sportActivity;
  @override
  String? get sportOfChoice;
  @override
  String? get specificSportActivity; // Limitations & Restrictions
  @override
  List<String> get physicalLimitations;
  @override
  List<String> get healthConditions;
  @override
  List<String> get exercisesToAvoid;
  @override
  List<String> get excludedExercises;
  @override
  String? get additionalHealthInfo; // Nutrition & Lifestyle
  @override
  List<String> get dietPreferences;
  @override
  List<String> get dietaryRestrictions;
  @override
  int? get mealsPerDay;
  @override
  int? get caloricGoal;
  @override
  bool? get takingSupplements;
  @override
  List<String> get supplements;
  @override
  String? get sleepQuality; // Progress Tracking
  @override
  bool get onboardingCompleted;
  @override
  bool get hasCompletedPreferences;
  @override
  bool get fitnessAssessmentCompleted; // Additional Data
  @override
  String? get fitnessGuide;
  @override
  String? get additionalNotes;
  @override
  Map<String, dynamic>? get metadata;

  /// Create a copy of UserProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UserProfileImplCopyWith<_$UserProfileImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
