import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_profile.freezed.dart';
part 'user_profile.g.dart';

/// Comprehensive user profile model matching Supabase schema
@freezed
class UserProfile with _$UserProfile {
  const factory UserProfile({
    required String id,
    String? email,
    String? displayName,
    required DateTime createdAt,
    required DateTime updatedAt,
    
    // Personal Information
    String? gender,
    int? age,
    double? height,
    @Default('cm') String heightUnit,
    double? weight,
    @Default('kg') String weightUnit,
    
    // Fitness Goals
    String? fitnessGoalPrimary,
    @Default([]) List<String> fitnessGoalsArray,
    @Default([]) List<String> fitnessGoalsOrder,
    
    // Fitness Experience & Levels
    String? trainingExperienceLevel,
    String? fitnessExperience,
    int? fitnessLevel,
    int? cardioFitnessLevel,
    int? weightliftingFitnessLevel,
    String? cardioLevel,
    String? cardioLevelDescription,
    String? weightliftingLevel,
    String? weightliftingLevelDescription,
    
    // Workout Preferences
    int? workoutFrequencyDays,
    int? preferredWorkoutDaysCount,
    @Default([]) List<String> workoutDays,
    String? workoutDurationPreference,
    String? preferredWorkoutDuration,
    
    // Exercise & Equipment
    @Default([]) List<String> exercisePreferences,
    @Default([]) List<String> equipment,
    @Default([]) List<String> workoutEnvironment,
    String? sportActivity,
    String? sportOfChoice,
    String? specificSportActivity,
    
    // Limitations & Restrictions
    @Default([]) List<String> physicalLimitations,
    @Default([]) List<String> healthConditions,
    @Default([]) List<String> exercisesToAvoid,
    @Default([]) List<String> excludedExercises,
    String? additionalHealthInfo,
    
    // Nutrition & Lifestyle
    @Default([]) List<String> dietPreferences,
    @Default([]) List<String> dietaryRestrictions,
    int? mealsPerDay,
    int? caloricGoal,
    bool? takingSupplements,
    @Default([]) List<String> supplements,
    String? sleepQuality,
    
    // Progress Tracking
    @Default(false) bool onboardingCompleted,
    @Default(false) bool hasCompletedPreferences,
    @Default(false) bool fitnessAssessmentCompleted,
    
    // Additional Data
    String? fitnessGuide,
    String? additionalNotes,
    Map<String, dynamic>? metadata,
  }) = _UserProfile;

  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);
}

/// Enum for fitness goals
enum FitnessGoal {
  optimizeHealth('Optimize My Health and Fitness'),
  sportTraining('Training for a Specific Sport or Activity'),
  buildMuscle('Build Muscle Mass and Size'),
  weightLoss('Weight Loss and Management'),
  increaseStamina('Increase Stamina'),
  increaseStrength('Increase Strength');

  const FitnessGoal(this.displayName);
  final String displayName;
}

/// Enum for workout environments
enum WorkoutEnvironment {
  largeGym('Large Gym (full equipment)'),
  smallGym('Small Gym (basic equipment)'),
  homeWithEquipment('Home with Equipment'),
  homeWithoutEquipment('Home without Equipment');

  const WorkoutEnvironment(this.displayName);
  final String displayName;
}

/// Enum for fitness levels
enum FitnessLevel {
  beginner(1, 'Beginner'),
  novice(2, 'Novice'),
  intermediate(3, 'Intermediate'),
  advanced(4, 'Advanced'),
  expert(5, 'Expert'),
  elite(6, 'Elite');

  const FitnessLevel(this.level, this.displayName);
  final int level;
  final String displayName;
}

/// Enum for workout duration preferences
enum WorkoutDuration {
  short(20, '20 minutes'),
  medium(30, '30 minutes'),
  standard(45, '45 minutes'),
  long(60, '60 minutes'),
  extended(90, '90 minutes'),
  flexible(0, 'No preference - optimize for effectiveness');

  const WorkoutDuration(this.minutes, this.displayName);
  final int minutes;
  final String displayName;
}

/// Extension methods for UserProfile
extension UserProfileExtensions on UserProfile {
  /// Check if basic profile information is complete
  bool get hasBasicInfo => 
      displayName != null && 
      gender != null && 
      age != null && 
      height != null && 
      weight != null;

  /// Check if fitness goals are set
  bool get hasFitnessGoals => 
      fitnessGoalPrimary != null && 
      fitnessGoalsArray.isNotEmpty;

  /// Check if fitness levels are assessed
  bool get hasFitnessLevels => 
      cardioFitnessLevel != null && 
      weightliftingFitnessLevel != null;

  /// Check if workout preferences are set
  bool get hasWorkoutPreferences => 
      workoutFrequencyDays != null && 
      preferredWorkoutDuration != null;

  /// Calculate overall profile completion percentage
  double get completionPercentage {
    int completed = 0;
    int total = 6;

    if (hasBasicInfo) completed++;
    if (hasFitnessGoals) completed++;
    if (hasFitnessLevels) completed++;
    if (hasWorkoutPreferences) completed++;
    if (workoutEnvironment.isNotEmpty) completed++;
    if (onboardingCompleted) completed++;

    return completed / total;
  }

  /// Get BMI if height and weight are available
  double? get bmi {
    if (height == null || weight == null) return null;
    
    double heightInMeters = heightUnit == 'cm' 
        ? height! / 100 
        : height! * 0.3048; // feet to meters
    
    double weightInKg = weightUnit == 'kg' 
        ? weight! 
        : weight! * 0.453592; // lbs to kg
    
    return weightInKg / (heightInMeters * heightInMeters);
  }

  /// Get BMI category
  String? get bmiCategory {
    final bmiValue = bmi;
    if (bmiValue == null) return null;
    
    if (bmiValue < 18.5) return 'Underweight';
    if (bmiValue < 25) return 'Normal weight';
    if (bmiValue < 30) return 'Overweight';
    return 'Obese';
  }
}
