import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/semantics.dart';
import '../theme/color_palette.dart';

/// Service for managing accessibility features
class AccessibilityService {
  static AccessibilityService? _instance;
  static AccessibilityService get instance => _instance ??= AccessibilityService._();
  AccessibilityService._();

  bool _isHighContrastEnabled = false;
  bool _isReduceMotionEnabled = false;
  bool _isLargeTextEnabled = false;
  double _textScaleFactor = 1.0;

  bool get isHighContrastEnabled => _isHighContrastEnabled;
  bool get isReduceMotionEnabled => _isReduceMotionEnabled;
  bool get isLargeTextEnabled => _isLargeTextEnabled;
  double get textScaleFactor => _textScaleFactor;

  /// Initialize accessibility settings from system
  Future<void> initialize() async {
    final platformDispatcher = WidgetsBinding.instance.platformDispatcher;
    
    _isReduceMotionEnabled = platformDispatcher.accessibilityFeatures.reduceMotion;
    _isHighContrastEnabled = platformDispatcher.accessibilityFeatures.highContrast;
    _textScaleFactor = platformDispatcher.textScaleFactor;
    _isLargeTextEnabled = _textScaleFactor > 1.3;
  }

  /// Toggle high contrast mode
  void toggleHighContrast() {
    _isHighContrastEnabled = !_isHighContrastEnabled;
    HapticFeedback.lightImpact();
  }

  /// Toggle reduce motion
  void toggleReduceMotion() {
    _isReduceMotionEnabled = !_isReduceMotionEnabled;
    HapticFeedback.lightImpact();
  }

  /// Get animation duration based on reduce motion setting
  Duration getAnimationDuration(Duration defaultDuration) {
    if (_isReduceMotionEnabled) {
      return Duration.zero;
    }
    return defaultDuration;
  }

  /// Get appropriate colors for high contrast mode
  ColorScheme getAccessibleColorScheme(ColorScheme baseScheme) {
    if (!_isHighContrastEnabled) {
      return baseScheme;
    }

    final isDark = baseScheme.brightness == Brightness.dark;
    
    return baseScheme.copyWith(
      primary: isDark ? AppColorPalette.highContrastDark : AppColorPalette.highContrastLight,
      onPrimary: isDark ? AppColorPalette.highContrastLight : AppColorPalette.highContrastDark,
      surface: isDark ? AppColorPalette.highContrastSurface : AppColorPalette.highContrastBackground,
      onSurface: isDark ? AppColorPalette.highContrastDark : AppColorPalette.highContrastLight,
    );
  }

  /// Get accessible text style with proper contrast
  TextStyle getAccessibleTextStyle(TextStyle baseStyle, BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    TextStyle accessibleStyle = baseStyle;
    
    if (_isHighContrastEnabled) {
      accessibleStyle = accessibleStyle.copyWith(
        color: isDark ? AppColorPalette.highContrastDark : AppColorPalette.highContrastLight,
        shadows: [
          Shadow(
            offset: const Offset(0, 1),
            blurRadius: 2,
            color: (isDark ? AppColorPalette.highContrastLight : AppColorPalette.highContrastDark)
                .withOpacity(0.3),
          ),
        ],
      );
    }
    
    if (_isLargeTextEnabled) {
      accessibleStyle = accessibleStyle.copyWith(
        fontSize: (accessibleStyle.fontSize ?? 14) * _textScaleFactor,
      );
    }
    
    return accessibleStyle;
  }

  /// Provide haptic feedback alternative for visual feedback
  void provideHapticFeedback(HapticFeedbackType type) {
    switch (type) {
      case HapticFeedbackType.light:
        HapticFeedback.lightImpact();
        break;
      case HapticFeedbackType.medium:
        HapticFeedback.mediumImpact();
        break;
      case HapticFeedbackType.heavy:
        HapticFeedback.heavyImpact();
        break;
      case HapticFeedbackType.selection:
        HapticFeedback.selectionClick();
        break;
    }
  }

  /// Check if touch target meets minimum size requirements (44x44pt)
  bool isValidTouchTarget(Size size) {
    const minTouchTarget = 44.0;
    return size.width >= minTouchTarget && size.height >= minTouchTarget;
  }

  /// Get semantic label for screen readers
  String getSemanticLabel({
    required String label,
    String? value,
    String? hint,
  }) {
    final parts = <String>[label];
    if (value != null) parts.add(value);
    if (hint != null) parts.add(hint);
    return parts.join(', ');
  }
}

/// Enum for haptic feedback types
enum HapticFeedbackType {
  light,
  medium,
  heavy,
  selection,
}

/// Accessible button widget with proper touch targets
class AccessibleButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final String? semanticLabel;
  final String? tooltip;
  final EdgeInsets? padding;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final BorderRadius? borderRadius;

  const AccessibleButton({
    super.key,
    required this.child,
    this.onPressed,
    this.semanticLabel,
    this.tooltip,
    this.padding,
    this.backgroundColor,
    this.foregroundColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final accessibilityService = AccessibilityService.instance;
    final theme = Theme.of(context);
    
    Widget button = Material(
      color: backgroundColor ?? theme.colorScheme.primary,
      borderRadius: borderRadius ?? BorderRadius.circular(8),
      child: InkWell(
        onTap: onPressed != null ? () {
          accessibilityService.provideHapticFeedback(HapticFeedbackType.light);
          onPressed!();
        } : null,
        borderRadius: borderRadius ?? BorderRadius.circular(8),
        child: Container(
          constraints: const BoxConstraints(
            minWidth: 44,
            minHeight: 44,
          ),
          padding: padding ?? const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
          child: Center(child: child),
        ),
      ),
    );

    if (semanticLabel != null) {
      button = Semantics(
        label: semanticLabel,
        button: true,
        enabled: onPressed != null,
        child: button,
      );
    }

    if (tooltip != null) {
      button = Tooltip(
        message: tooltip!,
        child: button,
      );
    }

    return button;
  }
}

/// Accessible card with proper semantics
class AccessibleCard extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final String? semanticLabel;
  final String? semanticValue;
  final String? semanticHint;
  final EdgeInsets? padding;
  final EdgeInsets? margin;

  const AccessibleCard({
    super.key,
    required this.child,
    this.onTap,
    this.semanticLabel,
    this.semanticValue,
    this.semanticHint,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final accessibilityService = AccessibilityService.instance;
    
    Widget card = Card(
      margin: margin,
      child: InkWell(
        onTap: onTap != null ? () {
          accessibilityService.provideHapticFeedback(HapticFeedbackType.light);
          onTap!();
        } : null,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          constraints: const BoxConstraints(
            minHeight: 44,
          ),
          padding: padding ?? const EdgeInsets.all(16),
          child: child,
        ),
      ),
    );

    if (semanticLabel != null) {
      final fullLabel = accessibilityService.getSemanticLabel(
        label: semanticLabel!,
        value: semanticValue,
        hint: semanticHint,
      );
      
      card = Semantics(
        label: fullLabel,
        button: onTap != null,
        enabled: onTap != null,
        child: card,
      );
    }

    return card;
  }
}

/// Accessible progress indicator with announcements
class AccessibleProgressIndicator extends StatefulWidget {
  final double progress;
  final String label;
  final bool announceProgress;
  final Duration announcementDelay;

  const AccessibleProgressIndicator({
    super.key,
    required this.progress,
    required this.label,
    this.announceProgress = true,
    this.announcementDelay = const Duration(milliseconds: 500),
  });

  @override
  State<AccessibleProgressIndicator> createState() => _AccessibleProgressIndicatorState();
}

class _AccessibleProgressIndicatorState extends State<AccessibleProgressIndicator> {
  double _lastAnnouncedProgress = 0.0;

  @override
  void didUpdateWidget(AccessibleProgressIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.announceProgress && 
        widget.progress != oldWidget.progress &&
        (widget.progress - _lastAnnouncedProgress).abs() >= 0.1) {
      
      Future.delayed(widget.announcementDelay, () {
        if (mounted) {
          final percentage = (widget.progress * 100).round();
          SemanticsService.announce(
            '${widget.label} $percentage percent complete',
            TextDirection.ltr,
          );
          _lastAnnouncedProgress = widget.progress;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final percentage = (widget.progress * 100).round();
    
    return Semantics(
      label: '${widget.label} $percentage percent complete',
      value: '$percentage%',
      child: LinearProgressIndicator(
        value: widget.progress,
        minHeight: 6,
      ),
    );
  }
}
