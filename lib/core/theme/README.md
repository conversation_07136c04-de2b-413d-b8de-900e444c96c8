# Theme System Documentation

## Overview

This theme system provides a comprehensive, scalable, and maintainable theming solution for the Flutter fitness app. It includes support for light/dark modes, responsive design, accessibility, and easy customization.

## Architecture

```
lib/core/theme/
├── app_theme.dart              # Main theme definitions
├── color_palette.dart          # Color system and tokens
├── typography.dart             # Text styles and typography
├── spacing.dart                # Spacing and sizing tokens
├── theme_config.dart           # Configuration and settings
├── theme_data.dart             # Data models and types
├── responsive.dart             # Responsive design utilities
├── providers/
│   └── theme_provider.dart     # State management
├── services/
│   └── theme_service.dart      # Business logic layer
└── testing/
    └── theme_test_utils.dart    # Testing utilities
```

## Key Features

### 🎨 **Comprehensive Theming**
- Light and dark mode support
- Material 3 compliance
- Semantic color tokens
- Typography hierarchy
- Consistent spacing system

### 📱 **Responsive Design**
- Breakpoint-based responsive utilities
- Device type detection
- Responsive layouts and components
- Adaptive spacing and sizing

### ⚙️ **Configuration Management**
- Centralized theme configuration
- Theme variants (standard, premium, accessibility)
- Persistent settings
- Import/export functionality

### 🧪 **Testing Support**
- Comprehensive testing utilities
- Mock services and providers
- Color contrast validation
- Responsive behavior testing

## Usage Examples

### Basic Theme Usage

```dart
// Using theme colors
Container(
  color: Theme.of(context).colorScheme.primary,
  child: Text(
    'Hello World',
    style: Theme.of(context).textTheme.headlineSmall,
  ),
)

// Using design tokens
Container(
  padding: AppSpacing.paddingMd,
  decoration: BoxDecoration(
    borderRadius: AppSpacing.borderRadiusLg,
    color: AppColorPalette.primaryBlue,
  ),
)
```

### Responsive Design

```dart
// Responsive values
final padding = Responsive.value(
  context,
  mobile: 16.0,
  tablet: 24.0,
  desktop: 32.0,
);

// Responsive layouts
ResponsiveLayout(
  mobile: MobileLayout(),
  tablet: TabletLayout(),
  desktop: DesktopLayout(),
)

// Responsive grid
ResponsiveGrid(
  children: items.map((item) => ItemCard(item)).toList(),
  mobileColumns: 1,
  tabletColumns: 2,
  desktopColumns: 3,
)
```

### Theme Management

```dart
// Change theme mode
ref.read(themeProvider.notifier).setThemeMode(AppThemeMode.dark);

// Toggle theme
ref.read(themeProvider.notifier).toggleTheme();

// Access current theme
final themeData = ref.watch(themeProvider);
final isDarkMode = themeData.isDarkMode;
```

### Using Themed Components

```dart
// Themed button
ThemedButton(
  text: 'Save',
  type: ButtonType.primary,
  onPressed: () => save(),
  icon: Icon(Icons.save),
)

// Themed card
ThemedCard(
  child: Column(
    children: [
      ThemedSectionHeader(title: 'Settings'),
      ThemedListTile(
        title: 'Dark Mode',
        trailing: Switch(value: isDarkMode, onChanged: toggleTheme),
      ),
    ],
  ),
)

// Themed text field
ThemedTextField(
  label: 'Email',
  hint: 'Enter your email',
  prefixIcon: Icon(Icons.email),
  validator: (value) => validateEmail(value),
)
```

## Configuration

### Theme Variants

```dart
// Standard theme
ThemeVariantConfig.standard

// Premium theme with enhanced features
ThemeVariantConfig.premium

// Accessibility-focused theme
ThemeVariantConfig.accessibility
```

### Custom Theme Configuration

```dart
class CustomThemeCustomization implements ThemeCustomization {
  @override
  ColorScheme get lightColorScheme => CustomColorPalette.lightScheme;
  
  @override
  ColorScheme get darkColorScheme => CustomColorPalette.darkScheme;
  
  @override
  TextTheme get textTheme => CustomTypography.textTheme;
  
  @override
  String get fontFamily => 'CustomFont';
  
  @override
  Map<String, dynamic> get customProperties => {
    'brandColor': Colors.purple,
    'accentColor': Colors.orange,
  };
}
```

## Testing

### Unit Tests

```dart
testWidgets('Theme switching works correctly', (tester) async {
  final mockPrefs = ThemeTestUtils.createMockPreferences();
  
  await tester.pumpWithProviders(
    MyApp(),
    mockPrefs: mockPrefs,
  );
  
  // Test theme switching
  final themeToggle = find.byType(ThemeToggleButton);
  await tester.tap(themeToggle);
  await tester.pumpAndSettle();
  
  // Verify theme changed
  expect(find.byIcon(Icons.light_mode), findsOneWidget);
});
```

### Color Contrast Testing

```dart
test('Color contrast meets accessibility standards', () {
  ThemeTestUtils.testColorContrast(
    foreground: AppColorPalette.lightColorScheme.onPrimary,
    background: AppColorPalette.lightColorScheme.primary,
    minimumRatio: 4.5,
  );
});
```

### Responsive Testing

```dart
testWidgets('Layout adapts to different screen sizes', (tester) async {
  await ThemeTestUtils.testResponsiveBehavior(
    tester,
    ResponsiveLayout(
      mobile: MobileView(),
      desktop: DesktopView(),
    ),
  );
});
```

## Best Practices

### 1. **Use Design Tokens**
Always use predefined spacing, colors, and typography tokens instead of hardcoded values.

```dart
// ✅ Good
Container(
  padding: AppSpacing.paddingMd,
  decoration: BoxDecoration(
    color: Theme.of(context).colorScheme.primary,
    borderRadius: AppSpacing.borderRadiusLg,
  ),
)

// ❌ Bad
Container(
  padding: EdgeInsets.all(16),
  decoration: BoxDecoration(
    color: Colors.blue,
    borderRadius: BorderRadius.circular(12),
  ),
)
```

### 2. **Responsive Design**
Use responsive utilities for layouts that work across different screen sizes.

```dart
// ✅ Good
ResponsiveContainer(
  child: ResponsiveGrid(
    children: items,
    mobileColumns: 1,
    desktopColumns: 3,
  ),
)

// ❌ Bad
Container(
  width: 300, // Fixed width
  child: GridView.count(
    crossAxisCount: 2, // Fixed columns
    children: items,
  ),
)
```

### 3. **Theme-Aware Components**
Create components that automatically adapt to theme changes.

```dart
// ✅ Good
class CustomCard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border.all(color: colorScheme.outline),
      ),
    );
  }
}
```

### 4. **Testing**
Always test theme-related functionality with proper test utilities.

```dart
// ✅ Good
testWidgets('Component adapts to dark theme', (tester) async {
  await tester.pumpWithTheme(
    MyComponent(),
    themeMode: ThemeMode.dark,
  );
  
  // Verify dark theme styling
  final container = tester.widget<Container>(find.byType(Container));
  expect(container.decoration.color, equals(darkTheme.colorScheme.surface));
});
```

## Migration Guide

### From Old Theme System

1. Replace hardcoded colors with theme colors
2. Use design tokens for spacing and sizing
3. Update components to use themed variants
4. Add responsive behavior where needed
5. Update tests to use theme test utilities

### Adding New Theme Variants

1. Create custom `ThemeCustomization` implementation
2. Define variant in `ThemeVariant` enum
3. Add configuration in `ThemeVariantConfig`
4. Update theme service to handle new variant
5. Add tests for new variant

## Performance Considerations

- Theme changes are optimized with efficient state management
- Responsive utilities use `LayoutBuilder` for optimal performance
- Color calculations are cached where possible
- Theme data is persisted efficiently using SharedPreferences

## Accessibility

- All color combinations meet WCAG contrast requirements
- Semantic labels are provided for screen readers
- Reduced motion support for accessibility preferences
- Touch target sizes meet accessibility guidelines
