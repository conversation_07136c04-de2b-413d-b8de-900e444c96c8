import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'theme_data.freezed.dart';
part 'theme_data.g.dart';

/// Enum for theme modes
enum AppThemeMode {
  light,
  dark,
  system,
}

/// Theme data model for the app
@freezed
class AppThemeData with _$AppThemeData {
  const factory AppThemeData({
    required AppThemeMode mode,
    required bool isDarkMode,
    @Default(false) bool useSystemTheme,
  }) = _AppThemeData;

  factory AppThemeData.fromJson(Map<String, dynamic> json) =>
      _$AppThemeDataFromJson(json);
}

/// Extension to get theme mode from system brightness
extension AppThemeModeExtension on AppThemeMode {
  ThemeMode get themeMode {
    switch (this) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }
  
  String get displayName {
    switch (this) {
      case AppThemeMode.light:
        return 'Light';
      case AppThemeMode.dark:
        return 'Dark';
      case AppThemeMode.system:
        return 'System';
    }
  }
  
  IconData get icon {
    switch (this) {
      case AppThemeMode.light:
        return Icons.light_mode;
      case AppThemeMode.dark:
        return Icons.dark_mode;
      case AppThemeMode.system:
        return Icons.brightness_auto;
    }
  }
}

/// Helper class to determine if current theme is dark
class ThemeHelper {
  static bool isDarkMode(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark;
  }
  
  static ColorScheme colorScheme(BuildContext context) {
    return Theme.of(context).colorScheme;
  }
  
  static TextTheme textTheme(BuildContext context) {
    return Theme.of(context).textTheme;
  }
  
  static Color surfaceColor(BuildContext context) {
    return Theme.of(context).colorScheme.surface;
  }
  
  static Color primaryColor(BuildContext context) {
    return Theme.of(context).colorScheme.primary;
  }
  
  static Color onSurfaceColor(BuildContext context) {
    return Theme.of(context).colorScheme.onSurface;
  }
  
  static Color cardColor(BuildContext context) {
    return Theme.of(context).cardColor;
  }
  
  static Color dividerColor(BuildContext context) {
    return Theme.of(context).dividerColor;
  }
}
