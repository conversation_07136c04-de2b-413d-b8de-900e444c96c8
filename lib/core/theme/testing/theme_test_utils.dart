import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../app_theme.dart';
import '../theme_data.dart';
import '../theme_config.dart';
import '../providers/theme_provider.dart';
import '../services/theme_service.dart';

/// Testing utilities for theme system
class ThemeTestUtils {
  /// Create a test widget with theme
  static Widget createTestWidget({
    required Widget child,
    ThemeData? theme,
    ThemeData? darkTheme,
    ThemeMode? themeMode,
  }) {
    return MaterialApp(
      theme: theme ?? AppTheme.lightTheme,
      darkTheme: darkTheme ?? AppTheme.darkTheme,
      themeMode: themeMode ?? ThemeMode.system,
      home: child,
    );
  }

  /// Create a test widget with Riverpod providers
  static Widget createTestWidgetWithProviders({
    required Widget child,
    List<Override>? overrides,
    SharedPreferences? mockPrefs,
  }) {
    final effectiveOverrides = <Override>[
      if (mockPrefs != null)
        sharedPreferencesProvider.overrideWithValue(mockPrefs),
      ...?overrides,
    ];

    return ProviderScope(
      overrides: effectiveOverrides,
      child: Consumer(
        builder: (context, ref, _) {
          final themeData = ref.watch(themeProvider);
          return MaterialApp(
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeData.mode.themeMode,
            home: child,
          );
        },
      ),
    );
  }

  /// Create mock SharedPreferences for testing
  static SharedPreferences createMockPreferences([
    Map<String, Object>? initialValues,
  ]) {
    SharedPreferences.setMockInitialValues(initialValues ?? {});
    return SharedPreferences.getInstance() as SharedPreferences;
  }

  /// Test theme color contrast ratios
  static void testColorContrast({
    required Color foreground,
    required Color background,
    double minimumRatio = 4.5,
  }) {
    final ratio = _calculateContrastRatio(foreground, background);
    expect(
      ratio,
      greaterThanOrEqualTo(minimumRatio),
      reason: 'Color contrast ratio $ratio is below minimum $minimumRatio',
    );
  }

  /// Calculate contrast ratio between two colors
  static double _calculateContrastRatio(Color color1, Color color2) {
    final luminance1 = _calculateLuminance(color1);
    final luminance2 = _calculateLuminance(color2);
    
    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;
    
    return (lighter + 0.05) / (darker + 0.05);
  }

  /// Calculate relative luminance of a color
  static double _calculateLuminance(Color color) {
    final r = _linearizeColorComponent(color.red / 255.0);
    final g = _linearizeColorComponent(color.green / 255.0);
    final b = _linearizeColorComponent(color.blue / 255.0);
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  /// Linearize color component for luminance calculation
  static double _linearizeColorComponent(double component) {
    if (component <= 0.03928) {
      return component / 12.92;
    } else {
      return math.pow((component + 0.055) / 1.055, 2.4).toDouble();
    }
  }

  /// Test theme consistency across components
  static void testThemeConsistency(WidgetTester tester) {
    // Test that all themed components use consistent colors
    final elevatedButtons = tester.widgetList<ElevatedButton>(find.byType(ElevatedButton));
    final cards = tester.widgetList<Card>(find.byType(Card));
    final textFields = tester.widgetList<TextField>(find.byType(TextField));

    // Verify consistent styling
    for (final button in elevatedButtons) {
      expect(button.style, isNotNull);
    }

    for (final card in cards) {
      expect(card.shape, isA<RoundedRectangleBorder>());
    }
  }

  /// Test responsive behavior
  static Future<void> testResponsiveBehavior(
    WidgetTester tester,
    Widget widget,
  ) async {
    // Test mobile size
    await tester.binding.setSurfaceSize(const Size(375, 667));
    await tester.pumpWidget(widget);
    await tester.pumpAndSettle();

    // Test tablet size
    await tester.binding.setSurfaceSize(const Size(768, 1024));
    await tester.pumpWidget(widget);
    await tester.pumpAndSettle();

    // Test desktop size
    await tester.binding.setSurfaceSize(const Size(1200, 800));
    await tester.pumpWidget(widget);
    await tester.pumpAndSettle();

    // Reset to default size
    await tester.binding.setSurfaceSize(null);
  }

  /// Create a mock theme service for testing
  static ThemeService createMockThemeService({
    AppThemeMode initialMode = AppThemeMode.system,
    bool shouldThrowErrors = false,
  }) {
    return MockThemeService(
      initialMode: initialMode,
      shouldThrowErrors: shouldThrowErrors,
    );
  }
}

/// Mock theme service for testing
class MockThemeService implements ThemeService {
  AppThemeMode _currentMode;
  final bool shouldThrowErrors;

  MockThemeService({
    AppThemeMode initialMode = AppThemeMode.system,
    this.shouldThrowErrors = false,
  }) : _currentMode = initialMode;

  @override
  Future<AppThemeMode> getThemeMode() async {
    if (shouldThrowErrors) throw Exception('Mock error');
    return _currentMode;
  }

  @override
  Future<void> setThemeMode(AppThemeMode mode) async {
    if (shouldThrowErrors) throw Exception('Mock error');
    _currentMode = mode;
  }

  @override
  Future<AppThemeSettings> getThemeSettings() async {
    if (shouldThrowErrors) throw Exception('Mock error');
    return const AppThemeSettings(
      useMaterial3: true,
      enableAnimations: true,
      enableHapticFeedback: true,
      enableSystemUIOverlay: true,
      defaultAnimationDuration: Duration(milliseconds: 300),
    );
  }

  @override
  Future<void> setThemeSettings(AppThemeSettings settings) async {
    if (shouldThrowErrors) throw Exception('Mock error');
    // Mock implementation
  }

  @override
  Future<void> clearThemeData() async {
    if (shouldThrowErrors) throw Exception('Mock error');
    _currentMode = AppThemeMode.system;
  }

  @override
  Future<Map<String, dynamic>> exportThemeConfig() async {
    if (shouldThrowErrors) throw Exception('Mock error');
    return {
      'version': '1.0.0',
      'themeMode': _currentMode.name,
      'settings': {},
      'variant': 'standard',
      'exportedAt': DateTime.now().toIso8601String(),
    };
  }

  @override
  Future<void> importThemeConfig(Map<String, dynamic> config) async {
    if (shouldThrowErrors) throw Exception('Mock error');
    // Mock implementation
  }

  @override
  void updateSystemUIOverlay(bool isDarkMode) {
    if (shouldThrowErrors) throw Exception('Mock error');
    // Mock implementation
  }

  @override
  Future<ThemeVariant> getThemeVariant() async {
    if (shouldThrowErrors) throw Exception('Mock error');
    return ThemeVariant.standard;
  }

  @override
  Future<void> setThemeVariant(ThemeVariant variant) async {
    if (shouldThrowErrors) throw Exception('Mock error');
    // Mock implementation
  }
}

/// Extension for easier testing
extension WidgetTesterThemeExtension on WidgetTester {
  /// Pump widget with theme
  Future<void> pumpWithTheme(
    Widget widget, {
    ThemeData? theme,
    ThemeData? darkTheme,
    ThemeMode? themeMode,
  }) async {
    await pumpWidget(
      ThemeTestUtils.createTestWidget(
        child: widget,
        theme: theme,
        darkTheme: darkTheme,
        themeMode: themeMode,
      ),
    );
  }

  /// Pump widget with providers
  Future<void> pumpWithProviders(
    Widget widget, {
    List<Override>? overrides,
    SharedPreferences? mockPrefs,
  }) async {
    await pumpWidget(
      ThemeTestUtils.createTestWidgetWithProviders(
        child: widget,
        overrides: overrides,
        mockPrefs: mockPrefs,
      ),
    );
  }
}
