// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'theme_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$AppThemeDataImpl _$$AppThemeDataImplFromJson(Map<String, dynamic> json) =>
    _$AppThemeDataImpl(
      mode: $enumDecode(_$AppThemeModeEnumMap, json['mode']),
      isDarkMode: json['isDarkMode'] as bool,
      useSystemTheme: json['useSystemTheme'] as bool? ?? false,
    );

Map<String, dynamic> _$$AppThemeDataImplToJson(_$AppThemeDataImpl instance) =>
    <String, dynamic>{
      'mode': _$AppThemeModeEnumMap[instance.mode]!,
      'isDarkMode': instance.isDarkMode,
      'useSystemTheme': instance.useSystemTheme,
    };

const _$AppThemeModeEnumMap = {
  AppThemeMode.light: 'light',
  AppThemeMode.dark: 'dark',
  AppThemeMode.system: 'system',
};
