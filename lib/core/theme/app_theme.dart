import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'color_palette.dart';
import 'typography.dart';
import 'spacing.dart';

/// Main theme configuration for the fitness app
class AppTheme {
  // Private constructor to prevent instantiation
  AppTheme._();
  
  /// Light theme configuration
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: AppColorPalette.lightColorScheme,
      textTheme: AppTypography.lightTextTheme,
      
      // App Bar Theme
      appBarTheme: AppBarTheme(
        elevation: 0,
        scrolledUnderElevation: 0,
        backgroundColor: AppColorPalette.lightColorScheme.surface,
        foregroundColor: AppColorPalette.lightColorScheme.onSurface,
        surfaceTintColor: Colors.transparent,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
        ),
        titleTextStyle: AppTypography.lightTextTheme.titleLarge?.copyWith(
          color: AppColorPalette.lightColorScheme.onSurface,
        ),
        iconTheme: IconThemeData(
          color: AppColorPalette.lightColorScheme.onSurface,
          size: AppSpacing.iconMd,
        ),
        actionsIconTheme: IconThemeData(
          color: AppColorPalette.lightColorScheme.onSurface,
          size: AppSpacing.iconMd,
        ),
      ),
      
      // Card Theme
      cardTheme: CardThemeData(
        elevation: 0,
        color: AppColorPalette.lightColorScheme.surface,
        surfaceTintColor: Colors.transparent,
        shadowColor: AppColorPalette.lightColorScheme.shadow.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: AppSpacing.borderRadiusLg,
          side: BorderSide(
            color: AppColorPalette.lightColorScheme.outline.withOpacity(0.2),
            width: 1,
          ),
        ),
        margin: AppSpacing.paddingVerticalSm,
      ),
      
      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColorPalette.lightColorScheme.primary,
          foregroundColor: AppColorPalette.lightColorScheme.onPrimary,
          elevation: 0,
          shadowColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: AppSpacing.borderRadiusLg,
          ),
          padding: AppSpacing.paddingMd,
          minimumSize: const Size(double.infinity, AppSpacing.buttonHeightMd),
          textStyle: AppTypography.lightTextTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      
      // Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColorPalette.lightColorScheme.primary,
          padding: AppSpacing.paddingMd,
          shape: RoundedRectangleBorder(
            borderRadius: AppSpacing.borderRadiusLg,
          ),
          textStyle: AppTypography.lightTextTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      
      // Outlined Button Theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColorPalette.lightColorScheme.primary,
          backgroundColor: Colors.transparent,
          elevation: 0,
          side: BorderSide(
            color: AppColorPalette.lightColorScheme.outline,
            width: 1,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: AppSpacing.borderRadiusLg,
          ),
          padding: AppSpacing.paddingMd,
          minimumSize: const Size(double.infinity, AppSpacing.buttonHeightMd),
          textStyle: AppTypography.lightTextTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      
      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColorPalette.lightColorScheme.surfaceVariant,
        border: OutlineInputBorder(
          borderRadius: AppSpacing.borderRadiusLg,
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: AppSpacing.borderRadiusLg,
          borderSide: BorderSide(
            color: AppColorPalette.lightColorScheme.outline.withOpacity(0.3),
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: AppSpacing.borderRadiusLg,
          borderSide: BorderSide(
            color: AppColorPalette.lightColorScheme.primary,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: AppSpacing.borderRadiusLg,
          borderSide: BorderSide(
            color: AppColorPalette.lightColorScheme.error,
            width: 1,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: AppSpacing.borderRadiusLg,
          borderSide: BorderSide(
            color: AppColorPalette.lightColorScheme.error,
            width: 2,
          ),
        ),
        contentPadding: AppSpacing.paddingMd,
        hintStyle: AppTypography.lightTextTheme.bodyMedium?.copyWith(
          color: AppColorPalette.lightColorScheme.onSurfaceVariant,
        ),
        labelStyle: AppTypography.lightTextTheme.bodyMedium?.copyWith(
          color: AppColorPalette.lightColorScheme.onSurfaceVariant,
        ),
        floatingLabelStyle: AppTypography.lightTextTheme.bodySmall?.copyWith(
          color: AppColorPalette.lightColorScheme.primary,
        ),
      ),
      
      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: AppColorPalette.lightColorScheme.surface,
        selectedItemColor: AppColorPalette.lightColorScheme.primary,
        unselectedItemColor: AppColorPalette.lightColorScheme.onSurfaceVariant,
        type: BottomNavigationBarType.fixed,
        elevation: 0,
        selectedLabelStyle: AppTypography.lightTextTheme.labelSmall,
        unselectedLabelStyle: AppTypography.lightTextTheme.labelSmall,
      ),
      
      // Floating Action Button Theme
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: AppColorPalette.lightColorScheme.primary,
        foregroundColor: AppColorPalette.lightColorScheme.onPrimary,
        elevation: 0,
        focusElevation: 0,
        hoverElevation: 0,
        highlightElevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: AppSpacing.borderRadiusLg,
        ),
      ),
      
      // List Tile Theme
      listTileTheme: ListTileThemeData(
        contentPadding: AppSpacing.paddingMd,
        shape: RoundedRectangleBorder(
          borderRadius: AppSpacing.borderRadiusLg,
        ),
        titleTextStyle: AppTypography.lightTextTheme.bodyLarge,
        subtitleTextStyle: AppTypography.lightTextTheme.bodyMedium,
        leadingAndTrailingTextStyle: AppTypography.lightTextTheme.labelMedium,
      ),
      
      // Divider Theme
      dividerTheme: DividerThemeData(
        color: AppColorPalette.lightColorScheme.outline.withOpacity(0.2),
        thickness: 1,
        space: 1,
      ),
      
      // Icon Theme
      iconTheme: IconThemeData(
        color: AppColorPalette.lightColorScheme.onSurface,
        size: AppSpacing.iconMd,
      ),
      
      // Primary Icon Theme
      primaryIconTheme: IconThemeData(
        color: AppColorPalette.lightColorScheme.primary,
        size: AppSpacing.iconMd,
      ),
      
      // Scaffold Background Color
      scaffoldBackgroundColor: AppColorPalette.lightColorScheme.background,
      
      // Canvas Color
      canvasColor: AppColorPalette.lightColorScheme.surface,
      
      // Disabled Color
      disabledColor: AppColorPalette.lightColorScheme.onSurface.withOpacity(0.38),
      
      // Splash Factory
      splashFactory: InkRipple.splashFactory,
      
      // Visual Density
      visualDensity: VisualDensity.adaptivePlatformDensity,
    );
  }
  
  /// Dark theme configuration
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: AppColorPalette.darkColorScheme,
      textTheme: AppTypography.darkTextTheme,
      
      // App Bar Theme
      appBarTheme: AppBarTheme(
        elevation: 0,
        scrolledUnderElevation: 0,
        backgroundColor: AppColorPalette.darkColorScheme.surface,
        foregroundColor: AppColorPalette.darkColorScheme.onSurface,
        surfaceTintColor: Colors.transparent,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.dark,
        ),
        titleTextStyle: AppTypography.darkTextTheme.titleLarge?.copyWith(
          color: AppColorPalette.darkColorScheme.onSurface,
        ),
        iconTheme: IconThemeData(
          color: AppColorPalette.darkColorScheme.onSurface,
          size: AppSpacing.iconMd,
        ),
        actionsIconTheme: IconThemeData(
          color: AppColorPalette.darkColorScheme.onSurface,
          size: AppSpacing.iconMd,
        ),
      ),
      
      // Card Theme
      cardTheme: CardThemeData(
        elevation: 0,
        color: AppColorPalette.darkColorScheme.surface,
        surfaceTintColor: Colors.transparent,
        shadowColor: AppColorPalette.darkColorScheme.shadow.withOpacity(0.3),
        shape: RoundedRectangleBorder(
          borderRadius: AppSpacing.borderRadiusLg,
          side: BorderSide(
            color: AppColorPalette.darkColorScheme.outline.withOpacity(0.2),
            width: 1,
          ),
        ),
        margin: AppSpacing.paddingVerticalSm,
      ),
      
      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColorPalette.darkColorScheme.primary,
          foregroundColor: AppColorPalette.darkColorScheme.onPrimary,
          elevation: 0,
          shadowColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: AppSpacing.borderRadiusLg,
          ),
          padding: AppSpacing.paddingMd,
          minimumSize: const Size(double.infinity, AppSpacing.buttonHeightMd),
          textStyle: AppTypography.darkTextTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      
      // Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColorPalette.darkColorScheme.primary,
          padding: AppSpacing.paddingMd,
          shape: RoundedRectangleBorder(
            borderRadius: AppSpacing.borderRadiusLg,
          ),
          textStyle: AppTypography.darkTextTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      
      // Outlined Button Theme
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColorPalette.darkColorScheme.primary,
          backgroundColor: Colors.transparent,
          elevation: 0,
          side: BorderSide(
            color: AppColorPalette.darkColorScheme.outline,
            width: 1,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: AppSpacing.borderRadiusLg,
          ),
          padding: AppSpacing.paddingMd,
          minimumSize: const Size(double.infinity, AppSpacing.buttonHeightMd),
          textStyle: AppTypography.darkTextTheme.labelLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      
      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColorPalette.darkColorScheme.surfaceVariant,
        border: OutlineInputBorder(
          borderRadius: AppSpacing.borderRadiusLg,
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: AppSpacing.borderRadiusLg,
          borderSide: BorderSide(
            color: AppColorPalette.darkColorScheme.outline.withOpacity(0.3),
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: AppSpacing.borderRadiusLg,
          borderSide: BorderSide(
            color: AppColorPalette.darkColorScheme.primary,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: AppSpacing.borderRadiusLg,
          borderSide: BorderSide(
            color: AppColorPalette.darkColorScheme.error,
            width: 1,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: AppSpacing.borderRadiusLg,
          borderSide: BorderSide(
            color: AppColorPalette.darkColorScheme.error,
            width: 2,
          ),
        ),
        contentPadding: AppSpacing.paddingMd,
        hintStyle: AppTypography.darkTextTheme.bodyMedium?.copyWith(
          color: AppColorPalette.darkColorScheme.onSurfaceVariant,
        ),
        labelStyle: AppTypography.darkTextTheme.bodyMedium?.copyWith(
          color: AppColorPalette.darkColorScheme.onSurfaceVariant,
        ),
        floatingLabelStyle: AppTypography.darkTextTheme.bodySmall?.copyWith(
          color: AppColorPalette.darkColorScheme.primary,
        ),
      ),
      
      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: AppColorPalette.darkColorScheme.surface,
        selectedItemColor: AppColorPalette.darkColorScheme.primary,
        unselectedItemColor: AppColorPalette.darkColorScheme.onSurfaceVariant,
        type: BottomNavigationBarType.fixed,
        elevation: 0,
        selectedLabelStyle: AppTypography.darkTextTheme.labelSmall,
        unselectedLabelStyle: AppTypography.darkTextTheme.labelSmall,
      ),
      
      // Floating Action Button Theme
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: AppColorPalette.darkColorScheme.primary,
        foregroundColor: AppColorPalette.darkColorScheme.onPrimary,
        elevation: 0,
        focusElevation: 0,
        hoverElevation: 0,
        highlightElevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: AppSpacing.borderRadiusLg,
        ),
      ),
      
      // List Tile Theme
      listTileTheme: ListTileThemeData(
        contentPadding: AppSpacing.paddingMd,
        shape: RoundedRectangleBorder(
          borderRadius: AppSpacing.borderRadiusLg,
        ),
        titleTextStyle: AppTypography.darkTextTheme.bodyLarge,
        subtitleTextStyle: AppTypography.darkTextTheme.bodyMedium,
        leadingAndTrailingTextStyle: AppTypography.darkTextTheme.labelMedium,
      ),
      
      // Divider Theme
      dividerTheme: DividerThemeData(
        color: AppColorPalette.darkColorScheme.outline.withOpacity(0.2),
        thickness: 1,
        space: 1,
      ),
      
      // Icon Theme
      iconTheme: IconThemeData(
        color: AppColorPalette.darkColorScheme.onSurface,
        size: AppSpacing.iconMd,
      ),
      
      // Primary Icon Theme
      primaryIconTheme: IconThemeData(
        color: AppColorPalette.darkColorScheme.primary,
        size: AppSpacing.iconMd,
      ),
      
      // Scaffold Background Color
      scaffoldBackgroundColor: AppColorPalette.darkColorScheme.background,
      
      // Canvas Color
      canvasColor: AppColorPalette.darkColorScheme.surface,
      
      // Disabled Color
      disabledColor: AppColorPalette.darkColorScheme.onSurface.withOpacity(0.38),
      
      // Splash Factory
      splashFactory: InkRipple.splashFactory,
      
      // Visual Density
      visualDensity: VisualDensity.adaptivePlatformDensity,
    );
  }
}
