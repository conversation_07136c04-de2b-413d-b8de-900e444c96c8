import 'package:flutter/material.dart';
import 'color_palette.dart';
import 'typography.dart';
import 'spacing.dart';

/// Centralized theme configuration for easy customization and maintenance
class ThemeConfig {
  // Private constructor to prevent instantiation
  ThemeConfig._();

  /// Theme configuration version for migration purposes
  static const String version = '1.0.0';

  /// Default theme settings
  static const AppThemeSettings defaultSettings = AppThemeSettings(
    useMaterial3: true,
    enableAnimations: true,
    enableHapticFeedback: true,
    enableSystemUIOverlay: true,
    defaultAnimationDuration: Duration(milliseconds: 300),
  );

  /// Component theme configurations
  static const ComponentThemeConfig components = ComponentThemeConfig();

  /// Breakpoints for responsive design
  static const ResponsiveBreakpoints breakpoints = ResponsiveBreakpoints(
    mobile: 480,
    tablet: 768,
    desktop: 1024,
    largeDesktop: 1440,
  );

  /// Accessibility settings
  static const AccessibilityConfig accessibility = AccessibilityConfig(
    minimumTouchTargetSize: 44.0,
    minimumContrastRatio: 4.5,
    enableSemanticLabels: true,
    enableReducedMotion: false,
  );
}

/// Theme settings configuration
class AppThemeSettings {
  final bool useMaterial3;
  final bool enableAnimations;
  final bool enableHapticFeedback;
  final bool enableSystemUIOverlay;
  final Duration defaultAnimationDuration;

  const AppThemeSettings({
    required this.useMaterial3,
    required this.enableAnimations,
    required this.enableHapticFeedback,
    required this.enableSystemUIOverlay,
    required this.defaultAnimationDuration,
  });

  AppThemeSettings copyWith({
    bool? useMaterial3,
    bool? enableAnimations,
    bool? enableHapticFeedback,
    bool? enableSystemUIOverlay,
    Duration? defaultAnimationDuration,
  }) {
    return AppThemeSettings(
      useMaterial3: useMaterial3 ?? this.useMaterial3,
      enableAnimations: enableAnimations ?? this.enableAnimations,
      enableHapticFeedback: enableHapticFeedback ?? this.enableHapticFeedback,
      enableSystemUIOverlay: enableSystemUIOverlay ?? this.enableSystemUIOverlay,
      defaultAnimationDuration: defaultAnimationDuration ?? this.defaultAnimationDuration,
    );
  }
}

/// Component-specific theme configurations
class ComponentThemeConfig {
  const ComponentThemeConfig();

  /// Button configurations
  ButtonThemeConfig get buttons => const ButtonThemeConfig();

  /// Card configurations
  CardThemeConfig get cards => const CardThemeConfig();

  /// Input configurations
  InputThemeConfig get inputs => const InputThemeConfig();

  /// Navigation configurations
  NavigationThemeConfig get navigation => const NavigationThemeConfig();
}

/// Button theme configuration
class ButtonThemeConfig {
  const ButtonThemeConfig();

  double get height => AppSpacing.buttonHeightMd;
  double get heightSmall => AppSpacing.buttonHeightSm;
  double get heightLarge => AppSpacing.buttonHeightLg;
  EdgeInsets get padding => AppSpacing.paddingMd;
  BorderRadius get borderRadius => AppSpacing.borderRadiusLg;
  Duration get animationDuration => AppSpacing.animationNormal;
}

/// Card theme configuration
class CardThemeConfig {
  const CardThemeConfig();

  double get elevation => 0;
  BorderRadius get borderRadius => AppSpacing.borderRadiusLg;
  EdgeInsets get padding => AppSpacing.paddingMd;
  EdgeInsets get margin => AppSpacing.paddingVerticalSm;
}

/// Input theme configuration
class InputThemeConfig {
  const InputThemeConfig();

  double get height => AppSpacing.inputHeightMd;
  BorderRadius get borderRadius => AppSpacing.borderRadiusLg;
  EdgeInsets get contentPadding => AppSpacing.paddingMd;
  double get borderWidth => 1.0;
  double get focusedBorderWidth => 2.0;
}

/// Navigation theme configuration
class NavigationThemeConfig {
  const NavigationThemeConfig();

  double get bottomNavHeight => 80.0;
  double get appBarHeight => kToolbarHeight;
  double get drawerWidth => 280.0;
  EdgeInsets get appBarPadding => AppSpacing.paddingMd;
}

/// Responsive breakpoints
class ResponsiveBreakpoints {
  final double mobile;
  final double tablet;
  final double desktop;
  final double largeDesktop;

  const ResponsiveBreakpoints({
    required this.mobile,
    required this.tablet,
    required this.desktop,
    required this.largeDesktop,
  });

  /// Get current device type based on screen width
  DeviceType getDeviceType(double width) {
    if (width < mobile) return DeviceType.mobile;
    if (width < tablet) return DeviceType.tablet;
    if (width < desktop) return DeviceType.desktop;
    return DeviceType.largeDesktop;
  }
}

/// Device types for responsive design
enum DeviceType {
  mobile,
  tablet,
  desktop,
  largeDesktop,
}

/// Accessibility configuration
class AccessibilityConfig {
  final double minimumTouchTargetSize;
  final double minimumContrastRatio;
  final bool enableSemanticLabels;
  final bool enableReducedMotion;

  const AccessibilityConfig({
    required this.minimumTouchTargetSize,
    required this.minimumContrastRatio,
    required this.enableSemanticLabels,
    required this.enableReducedMotion,
  });
}

/// Theme customization interface for different app variants
abstract class ThemeCustomization {
  ColorScheme get lightColorScheme;
  ColorScheme get darkColorScheme;
  TextTheme get textTheme;
  String get fontFamily;
  Map<String, dynamic> get customProperties;
}

/// Default theme customization
class DefaultThemeCustomization implements ThemeCustomization {
  @override
  ColorScheme get lightColorScheme => AppColorPalette.lightColorScheme;

  @override
  ColorScheme get darkColorScheme => AppColorPalette.darkColorScheme;

  @override
  TextTheme get textTheme => AppTypography.lightTextTheme;

  @override
  String get fontFamily => AppTypography.primaryFontFamily;

  @override
  Map<String, dynamic> get customProperties => const {};
}

/// Theme variant for different app flavors (e.g., premium, basic)
enum ThemeVariant {
  standard,
  premium,
  accessibility,
}

/// Theme variant configuration
class ThemeVariantConfig {
  final ThemeVariant variant;
  final ThemeCustomization customization;
  final AppThemeSettings settings;

  const ThemeVariantConfig({
    required this.variant,
    required this.customization,
    required this.settings,
  });

  static ThemeVariantConfig get standard => ThemeVariantConfig(
    variant: ThemeVariant.standard,
    customization: DefaultThemeCustomization(),
    settings: ThemeConfig.defaultSettings,
  );

  static ThemeVariantConfig get premium => ThemeVariantConfig(
    variant: ThemeVariant.premium,
    customization: DefaultThemeCustomization(),
    settings: ThemeConfig.defaultSettings.copyWith(
      enableAnimations: true,
      enableHapticFeedback: true,
    ),
  );

  static ThemeVariantConfig get accessibility => ThemeVariantConfig(
    variant: ThemeVariant.accessibility,
    customization: DefaultThemeCustomization(),
    settings: ThemeConfig.defaultSettings.copyWith(
      enableAnimations: false,
      defaultAnimationDuration: Duration.zero,
    ),
  );
}
