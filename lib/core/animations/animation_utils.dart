import 'package:flutter/material.dart';
import 'package:flutter/physics.dart';

/// Animation utilities for the fitness app
/// Provides spring animations, counting effects, and performance-optimized controllers
class AnimationUtils {
  // Animation durations
  static const Duration fastDuration = Duration(milliseconds: 200);
  static const Duration normalDuration = Duration(milliseconds: 300);
  static const Duration slowDuration = Duration(milliseconds: 400);
  static const Duration extraSlowDuration = Duration(milliseconds: 600);
  
  // Spring simulation parameters
  static const SpringDescription defaultSpring = SpringDescription(
    mass: 1.0,
    stiffness: 500.0,
    damping: 30.0,
  );
  
  static const SpringDescription bouncySpring = SpringDescription(
    mass: 1.0,
    stiffness: 300.0,
    damping: 20.0,
  );
  
  static const SpringDescription gentleSpring = SpringDescription(
    mass: 1.0,
    stiffness: 200.0,
    damping: 40.0,
  );
  
  /// Create a spring animation controller
  static AnimationController createSpringController({
    required TickerProvider vsync,
    SpringDescription spring = defaultSpring,
    double initialValue = 0.0,
  }) {
    final controller = AnimationController(
      vsync: vsync,
      duration: normalDuration,
      value: initialValue,
    );
    
    return controller;
  }
  
  /// Create a spring simulation
  static SpringSimulation createSpringSimulation({
    SpringDescription spring = defaultSpring,
    double start = 0.0,
    double end = 1.0,
    double velocity = 0.0,
  }) {
    return SpringSimulation(spring, start, end, velocity);
  }
  
  /// Animate to value with spring physics
  static TickerFuture animateToWithSpring(
    AnimationController controller, {
    required double target,
    SpringDescription spring = defaultSpring,
    double velocity = 0.0,
  }) {
    final simulation = SpringSimulation(
      spring,
      controller.value,
      target,
      velocity,
    );
    
    return controller.animateWith(simulation);
  }
  
  /// Create staggered animations for multiple elements
  static List<Animation<double>> createStaggeredAnimations({
    required AnimationController controller,
    required int count,
    Duration staggerDelay = const Duration(milliseconds: 50),
    Curve curve = Curves.easeOutCubic,
  }) {
    final animations = <Animation<double>>[];
    final totalStaggerTime = staggerDelay.inMilliseconds * (count - 1);
    final animationDuration = controller.duration!.inMilliseconds;
    
    for (int i = 0; i < count; i++) {
      final startTime = (staggerDelay.inMilliseconds * i) / animationDuration;
      final endTime = (animationDuration - totalStaggerTime + staggerDelay.inMilliseconds * i) / animationDuration;
      
      final animation = Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(
        CurvedAnimation(
          parent: controller,
          curve: Interval(
            startTime.clamp(0.0, 1.0),
            endTime.clamp(0.0, 1.0),
            curve: curve,
          ),
        ),
      );
      
      animations.add(animation);
    }
    
    return animations;
  }
  
  /// Create a scale animation with spring physics
  static Animation<double> createScaleAnimation({
    required AnimationController controller,
    double begin = 0.0,
    double end = 1.0,
    Curve curve = Curves.elasticOut,
  }) {
    return Tween<double>(
      begin: begin,
      end: end,
    ).animate(
      CurvedAnimation(
        parent: controller,
        curve: curve,
      ),
    );
  }
  
  /// Create a slide animation
  static Animation<Offset> createSlideAnimation({
    required AnimationController controller,
    Offset begin = const Offset(0.0, 1.0),
    Offset end = Offset.zero,
    Curve curve = Curves.easeOutCubic,
  }) {
    return Tween<Offset>(
      begin: begin,
      end: end,
    ).animate(
      CurvedAnimation(
        parent: controller,
        curve: curve,
      ),
    );
  }
  
  /// Create a fade animation
  static Animation<double> createFadeAnimation({
    required AnimationController controller,
    double begin = 0.0,
    double end = 1.0,
    Curve curve = Curves.easeInOut,
  }) {
    return Tween<double>(
      begin: begin,
      end: end,
    ).animate(
      CurvedAnimation(
        parent: controller,
        curve: curve,
      ),
    );
  }
  
  /// Create a rotation animation
  static Animation<double> createRotationAnimation({
    required AnimationController controller,
    double begin = 0.0,
    double end = 1.0,
    Curve curve = Curves.easeInOut,
  }) {
    return Tween<double>(
      begin: begin,
      end: end,
    ).animate(
      CurvedAnimation(
        parent: controller,
        curve: curve,
      ),
    );
  }
  
  /// Performance-optimized animation builder
  static Widget buildOptimizedAnimation({
    required Animation<double> animation,
    required Widget Function(BuildContext context, double value) builder,
    Widget? child,
  }) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) => builder(context, animation.value),
      child: child,
    );
  }
  
  /// Dispose multiple controllers safely
  static void disposeControllers(List<AnimationController> controllers) {
    for (final controller in controllers) {
      controller.dispose();
    }
  }
}

/// Custom curves for fitness app animations
class FitnessAnimationCurves {
  static const Curve energyBurst = Curves.elasticOut;
  static const Curve achievementPop = Curves.bounceOut;
  static const Curve smoothEntry = Curves.easeOutCubic;
  static const Curve quickExit = Curves.easeInCubic;
  static const Curve dataVisualization = Curves.easeInOutCubic;
  static const Curve cardHover = Curves.easeOutQuart;
  static const Curve navigationTransition = Curves.easeInOutQuint;
  
  /// Custom cubic bezier curve for spring-like motion
  static const Curve springCurve = Cubic(0.25, 0.46, 0.45, 0.94);
  
  /// Custom curve for number counting animations
  static const Curve countingCurve = Cubic(0.4, 0.0, 0.2, 1.0);
}
