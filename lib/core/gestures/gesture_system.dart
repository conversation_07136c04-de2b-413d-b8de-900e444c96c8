import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../animations/animation_utils.dart';

/// Enhanced gesture system with haptic feedback and natural interactions
class GestureSystem {
  static const Duration _hapticDelay = Duration(milliseconds: 10);
  
  /// Create a swipe gesture detector with haptic feedback
  static Widget createSwipeDetector({
    required Widget child,
    VoidCallback? onSwipeLeft,
    VoidCallback? onSwipeRight,
    VoidCallback? onSwipeUp,
    VoidCallback? onSwipeDown,
    double sensitivity = 100.0,
    bool enableHapticFeedback = true,
  }) {
    return GestureDetector(
      onPanEnd: (details) {
        final velocity = details.velocity.pixelsPerSecond;
        final dx = velocity.dx.abs();
        final dy = velocity.dy.abs();
        
        if (dx > sensitivity || dy > sensitivity) {
          if (enableHapticFeedback) {
            HapticFeedback.lightImpact();
          }
          
          if (dx > dy) {
            // Horizontal swipe
            if (velocity.dx > 0) {
              onSwipeRight?.call();
            } else {
              onSwipeLeft?.call();
            }
          } else {
            // Vertical swipe
            if (velocity.dy > 0) {
              onSwipeDown?.call();
            } else {
              onSwipeUp?.call();
            }
          }
        }
      },
      child: child,
    );
  }
  
  /// Create a long press detector with progressive haptic feedback
  static Widget createLongPressDetector({
    required Widget child,
    required VoidCallback onLongPress,
    VoidCallback? onLongPressStart,
    VoidCallback? onLongPressEnd,
    Duration longPressDuration = const Duration(milliseconds: 500),
    bool enableProgressiveHaptic = true,
  }) {
    return GestureDetector(
      onLongPressStart: (details) {
        if (enableProgressiveHaptic) {
          HapticFeedback.lightImpact();
          Future.delayed(longPressDuration ~/ 2, () {
            HapticFeedback.mediumImpact();
          });
        }
        onLongPressStart?.call();
      },
      onLongPress: () {
        if (enableProgressiveHaptic) {
          HapticFeedback.heavyImpact();
        }
        onLongPress();
      },
      onLongPressEnd: (details) {
        onLongPressEnd?.call();
      },
      child: child,
    );
  }
  
  /// Create a pinch-to-zoom detector
  static Widget createPinchZoomDetector({
    required Widget child,
    required ValueChanged<double> onScaleUpdate,
    VoidCallback? onScaleStart,
    VoidCallback? onScaleEnd,
    double minScale = 0.5,
    double maxScale = 3.0,
    bool enableHapticFeedback = true,
  }) {
    return GestureDetector(
      onScaleStart: (details) {
        if (enableHapticFeedback) {
          HapticFeedback.lightImpact();
        }
        onScaleStart?.call();
      },
      onScaleUpdate: (details) {
        final scale = details.scale.clamp(minScale, maxScale);
        onScaleUpdate(scale);
      },
      onScaleEnd: (details) {
        if (enableHapticFeedback) {
          HapticFeedback.lightImpact();
        }
        onScaleEnd?.call();
      },
      child: child,
    );
  }
}

/// Enhanced pull-to-refresh with physics-based feedback
class PhysicsBasedRefresh extends StatefulWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final double triggerDistance;
  final Color? indicatorColor;
  final bool enableHapticFeedback;

  const PhysicsBasedRefresh({
    super.key,
    required this.child,
    required this.onRefresh,
    this.triggerDistance = 80.0,
    this.indicatorColor,
    this.enableHapticFeedback = true,
  });

  @override
  State<PhysicsBasedRefresh> createState() => _PhysicsBasedRefreshState();
}

class _PhysicsBasedRefreshState extends State<PhysicsBasedRefresh>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  bool _isRefreshing = false;
  bool _hasTriggeredHaptic = false;
  double _dragDistance = 0.0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: AnimationUtils.normalDuration,
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: FitnessAnimationCurves.springCurve,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    if (_isRefreshing) return;
    
    setState(() {
      _dragDistance = (_dragDistance + details.delta.dy).clamp(0.0, widget.triggerDistance * 1.5);
    });
    
    final progress = _dragDistance / widget.triggerDistance;
    _controller.value = progress.clamp(0.0, 1.0);
    
    // Trigger haptic feedback when reaching threshold
    if (widget.enableHapticFeedback && 
        progress >= 1.0 && 
        !_hasTriggeredHaptic) {
      HapticFeedback.mediumImpact();
      _hasTriggeredHaptic = true;
    }
  }

  void _handlePanEnd(DragEndDetails details) {
    if (_isRefreshing) return;
    
    if (_dragDistance >= widget.triggerDistance) {
      _triggerRefresh();
    } else {
      _resetRefresh();
    }
  }

  Future<void> _triggerRefresh() async {
    setState(() {
      _isRefreshing = true;
    });
    
    if (widget.enableHapticFeedback) {
      HapticFeedback.heavyImpact();
    }
    
    _controller.forward();
    
    try {
      await widget.onRefresh();
    } finally {
      await _resetRefresh();
    }
  }

  Future<void> _resetRefresh() async {
    await _controller.reverse();
    setState(() {
      _isRefreshing = false;
      _dragDistance = 0.0;
      _hasTriggeredHaptic = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Stack(
      children: [
        GestureDetector(
          onPanUpdate: _handlePanUpdate,
          onPanEnd: _handlePanEnd,
          child: widget.child,
        ),
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          child: AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(0, -50 + (50 * _animation.value)),
                child: Opacity(
                  opacity: _animation.value,
                  child: Container(
                    height: 50,
                    alignment: Alignment.center,
                    child: CircularProgressIndicator(
                      value: _isRefreshing ? null : _animation.value,
                      color: widget.indicatorColor ?? theme.colorScheme.primary,
                      strokeWidth: 3,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

/// Interactive button with advanced gesture recognition
class InteractiveButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onDoubleTap;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final bool enableHapticFeedback;
  final bool enableScaleAnimation;

  const InteractiveButton({
    super.key,
    required this.child,
    this.onTap,
    this.onLongPress,
    this.onDoubleTap,
    this.padding,
    this.borderRadius,
    this.backgroundColor,
    this.enableHapticFeedback = true,
    this.enableScaleAnimation = true,
  });

  @override
  State<InteractiveButton> createState() => _InteractiveButtonState();
}

class _InteractiveButtonState extends State<InteractiveButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: AnimationUtils.fastDuration,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: FitnessAnimationCurves.cardHover,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    if (widget.enableScaleAnimation) {
      _controller.forward();
    }
    if (widget.enableHapticFeedback) {
      HapticFeedback.lightImpact();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    _resetButton();
    widget.onTap?.call();
  }

  void _handleTapCancel() {
    _resetButton();
  }

  void _handleLongPress() {
    if (widget.enableHapticFeedback) {
      HapticFeedback.mediumImpact();
    }
    widget.onLongPress?.call();
  }

  void _handleDoubleTap() {
    if (widget.enableHapticFeedback) {
      HapticFeedback.lightImpact();
    }
    widget.onDoubleTap?.call();
  }

  void _resetButton() {
    setState(() => _isPressed = false);
    if (widget.enableScaleAnimation) {
      _controller.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.enableScaleAnimation ? _scaleAnimation.value : 1.0,
          child: GestureDetector(
            onTapDown: _handleTapDown,
            onTapUp: _handleTapUp,
            onTapCancel: _handleTapCancel,
            onLongPress: widget.onLongPress != null ? _handleLongPress : null,
            onDoubleTap: widget.onDoubleTap != null ? _handleDoubleTap : null,
            child: Container(
              padding: widget.padding ?? const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: widget.backgroundColor ?? theme.colorScheme.surface,
                borderRadius: widget.borderRadius ?? BorderRadius.circular(8),
                boxShadow: _isPressed ? [] : [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: widget.child,
            ),
          ),
        );
      },
    );
  }
}

/// One-handed operation helper
class OneHandedHelper {
  static bool isOneHandedMode(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.size.height > 700; // Heuristic for large screens
  }
  
  static EdgeInsets getOneHandedPadding(BuildContext context) {
    if (isOneHandedMode(context)) {
      return const EdgeInsets.only(bottom: 100);
    }
    return EdgeInsets.zero;
  }
  
  static Widget wrapForOneHanded(BuildContext context, Widget child) {
    return Padding(
      padding: getOneHandedPadding(context),
      child: child,
    );
  }
}
