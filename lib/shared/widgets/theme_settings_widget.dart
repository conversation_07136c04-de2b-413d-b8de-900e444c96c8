import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/theme/providers/theme_provider.dart';
import '../../core/theme/theme_data.dart';
import '../../core/theme/spacing.dart';

/// Widget for theme settings and mode switching
class ThemeSettingsWidget extends ConsumerWidget {
  const ThemeSettingsWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentTheme = ref.watch(themeProvider);
    final themeModes = ref.watch(themeModeOptionsProvider);

    return Card(
      child: Padding(
        padding: AppSpacing.paddingMd,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Theme Settings',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            AppSpacing.gapVerticalMd,
            Text(
              'Choose your preferred theme mode',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            AppSpacing.gapVerticalMd,
            ...themeModes.map((mode) => _ThemeModeOption(
              mode: mode,
              isSelected: currentTheme.mode == mode,
              onTap: () => ref.read(themeProvider.notifier).setThemeMode(mode),
            )),
          ],
        ),
      ),
    );
  }
}

/// Individual theme mode option
class _ThemeModeOption extends StatelessWidget {
  final AppThemeMode mode;
  final bool isSelected;
  final VoidCallback onTap;

  const _ThemeModeOption({
    required this.mode,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return InkWell(
      onTap: onTap,
      borderRadius: AppSpacing.borderRadiusLg,
      child: Container(
        padding: AppSpacing.paddingMd,
        margin: AppSpacing.paddingVerticalXs,
        decoration: BoxDecoration(
          borderRadius: AppSpacing.borderRadiusLg,
          border: Border.all(
            color: isSelected 
                ? colorScheme.primary 
                : colorScheme.outline.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
          color: isSelected 
              ? colorScheme.primaryContainer.withOpacity(0.1)
              : Colors.transparent,
        ),
        child: Row(
          children: [
            Icon(
              mode.icon,
              color: isSelected 
                  ? colorScheme.primary 
                  : colorScheme.onSurfaceVariant,
              size: AppSpacing.iconMd,
            ),
            AppSpacing.gapHorizontalMd,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    mode.displayName,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: isSelected 
                          ? colorScheme.primary 
                          : colorScheme.onSurface,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
                  ),
                  Text(
                    _getDescription(mode),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: colorScheme.primary,
                size: AppSpacing.iconSm,
              ),
          ],
        ),
      ),
    );
  }

  String _getDescription(AppThemeMode mode) {
    switch (mode) {
      case AppThemeMode.light:
        return 'Always use light theme';
      case AppThemeMode.dark:
        return 'Always use dark theme';
      case AppThemeMode.system:
        return 'Follow system settings';
    }
  }
}

/// Simple theme toggle button for quick switching
class ThemeToggleButton extends ConsumerWidget {
  const ThemeToggleButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isDarkMode = ref.watch(isDarkModeProvider);
    final useSystemTheme = ref.watch(useSystemThemeProvider);

    return IconButton(
      onPressed: useSystemTheme 
          ? null 
          : () => ref.read(themeProvider.notifier).toggleTheme(),
      icon: AnimatedSwitcher(
        duration: AppSpacing.animationNormal,
        child: Icon(
          isDarkMode ? Icons.light_mode : Icons.dark_mode,
          key: ValueKey(isDarkMode),
        ),
      ),
      tooltip: useSystemTheme 
          ? 'Using system theme' 
          : (isDarkMode ? 'Switch to light mode' : 'Switch to dark mode'),
    );
  }
}

/// Theme preview widget showing colors
class ThemePreviewWidget extends ConsumerWidget {
  const ThemePreviewWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Card(
      child: Padding(
        padding: AppSpacing.paddingMd,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Theme Preview',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            AppSpacing.gapVerticalMd,
            Row(
              children: [
                _ColorSwatch(
                  label: 'Primary',
                  color: colorScheme.primary,
                ),
                AppSpacing.gapHorizontalMd,
                _ColorSwatch(
                  label: 'Secondary',
                  color: colorScheme.secondary,
                ),
                AppSpacing.gapHorizontalMd,
                _ColorSwatch(
                  label: 'Surface',
                  color: colorScheme.surface,
                  borderColor: colorScheme.outline,
                ),
              ],
            ),
            AppSpacing.gapVerticalMd,
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {},
                    child: const Text('Primary Button'),
                  ),
                ),
                AppSpacing.gapHorizontalMd,
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {},
                    child: const Text('Outlined'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

/// Color swatch for theme preview
class _ColorSwatch extends StatelessWidget {
  final String label;
  final Color color;
  final Color? borderColor;

  const _ColorSwatch({
    required this.label,
    required this.color,
    this.borderColor,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color,
            borderRadius: AppSpacing.borderRadiusSm,
            border: borderColor != null 
                ? Border.all(color: borderColor!, width: 1)
                : null,
          ),
        ),
        AppSpacing.gapVerticalXs,
        Text(
          label,
          style: Theme.of(context).textTheme.labelSmall,
        ),
      ],
    );
  }
}
