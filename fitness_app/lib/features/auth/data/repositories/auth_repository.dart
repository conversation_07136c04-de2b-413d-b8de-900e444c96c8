import "package:supabase_flutter/supabase_flutter.dart";
import "../../../../shared/services/supabase_service.dart";

class AuthRepository {
  Future<void> signUp({
    required String email,
    required String password,
  }) async {
    try {
      await SupabaseService.signUp(
        email: email,
        password: password,
      );
    } catch (e) {
      throw Exception("Failed to sign up: $e");
    }
  }

  Future<void> signIn({
    required String email,
    required String password,
  }) async {
    try {
      await SupabaseService.signIn(
        email: email,
        password: password,
      );
    } catch (e) {
      throw Exception("Failed to sign in: $e");
    }
  }

  Future<void> signOut() async {
    try {
      await SupabaseService.signOut();
    } catch (e) {
      throw Exception("Failed to sign out: $e");
    }
  }

  User? get currentUser => SupabaseService.currentUser;

  Stream<AuthState> get authStateChanges => SupabaseService.authStateChanges;
}
