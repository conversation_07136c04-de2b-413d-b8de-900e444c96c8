import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:fitness_app/core/theme/app_theme.dart';
import 'package:fitness_app/core/theme/theme_data.dart';
import 'package:fitness_app/core/theme/theme_config.dart';
import 'package:fitness_app/core/theme/providers/theme_provider.dart';
import 'package:fitness_app/core/theme/color_palette.dart';
import 'package:fitness_app/core/theme/responsive.dart';
import 'package:fitness_app/core/theme/services/theme_service.dart';
import 'package:fitness_app/core/theme/testing/theme_test_utils.dart';
import 'package:fitness_app/shared/widgets/themed_components.dart';

void main() {
  group('Theme System Tests', () {
    late SharedPreferences prefs;

    setUp(() async {
      SharedPreferences.setMockInitialValues({});
      prefs = await SharedPreferences.getInstance();
    });

    testWidgets('Light theme should have correct colors', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Builder(
            builder: (context) {
              final colorScheme = Theme.of(context).colorScheme;
              
              // Test that light theme colors are correct
              expect(colorScheme.brightness, Brightness.light);
              expect(colorScheme.primary, AppColorPalette.lightColorScheme.primary);
              expect(colorScheme.surface, AppColorPalette.lightColorScheme.surface);
              
              return const Scaffold(
                body: Text('Light Theme Test'),
              );
            },
          ),
        ),
      );
    });

    testWidgets('Dark theme should have correct colors', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.darkTheme,
          home: Builder(
            builder: (context) {
              final colorScheme = Theme.of(context).colorScheme;
              
              // Test that dark theme colors are correct
              expect(colorScheme.brightness, Brightness.dark);
              expect(colorScheme.primary, AppColorPalette.darkColorScheme.primary);
              expect(colorScheme.surface, AppColorPalette.darkColorScheme.surface);
              
              return const Scaffold(
                body: Text('Dark Theme Test'),
              );
            },
          ),
        ),
      );
    });

    test('Theme provider should initialize with system theme', () {
      final container = ProviderContainer(
        overrides: [
          sharedPreferencesProvider.overrideWithValue(prefs),
        ],
      );

      final themeData = container.read(themeProvider);
      
      expect(themeData.mode, AppThemeMode.system);
      expect(themeData.useSystemTheme, true);
      
      container.dispose();
    });

    test('Theme provider should persist theme changes', () async {
      final container = ProviderContainer(
        overrides: [
          sharedPreferencesProvider.overrideWithValue(prefs),
        ],
      );

      final themeNotifier = container.read(themeProvider.notifier);
      
      // Change to dark mode
      await themeNotifier.setThemeMode(AppThemeMode.dark);
      
      final themeData = container.read(themeProvider);
      expect(themeData.mode, AppThemeMode.dark);
      expect(themeData.isDarkMode, true);
      expect(themeData.useSystemTheme, false);
      
      container.dispose();
    });

    test('Theme provider should toggle between light and dark', () async {
      final container = ProviderContainer(
        overrides: [
          sharedPreferencesProvider.overrideWithValue(prefs),
        ],
      );

      final themeNotifier = container.read(themeProvider.notifier);
      
      // Start with light mode
      await themeNotifier.setThemeMode(AppThemeMode.light);
      expect(container.read(themeProvider).isDarkMode, false);
      
      // Toggle to dark
      await themeNotifier.toggleTheme();
      expect(container.read(themeProvider).isDarkMode, true);
      expect(container.read(themeProvider).mode, AppThemeMode.dark);
      
      // Toggle back to light
      await themeNotifier.toggleTheme();
      expect(container.read(themeProvider).isDarkMode, false);
      expect(container.read(themeProvider).mode, AppThemeMode.light);
      
      container.dispose();
    });

    test('AppThemeMode extension should return correct ThemeMode', () {
      expect(AppThemeMode.light.themeMode, ThemeMode.light);
      expect(AppThemeMode.dark.themeMode, ThemeMode.dark);
      expect(AppThemeMode.system.themeMode, ThemeMode.system);
    });

    test('AppThemeMode extension should return correct display names', () {
      expect(AppThemeMode.light.displayName, 'Light');
      expect(AppThemeMode.dark.displayName, 'Dark');
      expect(AppThemeMode.system.displayName, 'System');
    });

    test('AppThemeMode extension should return correct icons', () {
      expect(AppThemeMode.light.icon, Icons.light_mode);
      expect(AppThemeMode.dark.icon, Icons.dark_mode);
      expect(AppThemeMode.system.icon, Icons.brightness_auto);
    });
  });

  group('Color Palette Tests', () {
    test('Light and dark color schemes should have different brightness', () {
      expect(AppColorPalette.lightColorScheme.brightness, Brightness.light);
      expect(AppColorPalette.darkColorScheme.brightness, Brightness.dark);
    });

    test('Primary colors should be consistent across themes', () {
      // Primary colors should be different between light and dark themes
      expect(
        AppColorPalette.lightColorScheme.primary,
        isNot(equals(AppColorPalette.darkColorScheme.primary)),
      );
    });

    test('Gradients should be properly defined', () {
      expect(AppColorPalette.primaryGradient.colors.length, 2);
      expect(AppColorPalette.secondaryGradient.colors.length, 2);
      expect(AppColorPalette.successGradient.colors.length, 2);
    });

    test('Color contrast meets accessibility standards', () {
      ThemeTestUtils.testColorContrast(
        foreground: AppColorPalette.lightColorScheme.onPrimary,
        background: AppColorPalette.lightColorScheme.primary,
        minimumRatio: 4.5,
      );

      ThemeTestUtils.testColorContrast(
        foreground: AppColorPalette.darkColorScheme.onPrimary,
        background: AppColorPalette.darkColorScheme.primary,
        minimumRatio: 4.5,
      );
    });
  });

  group('Theme Configuration Tests', () {
    test('Default theme settings should be properly configured', () {
      final settings = ThemeConfig.defaultSettings;

      expect(settings.useMaterial3, true);
      expect(settings.enableAnimations, true);
      expect(settings.enableHapticFeedback, true);
      expect(settings.enableSystemUIOverlay, true);
      expect(settings.defaultAnimationDuration, const Duration(milliseconds: 300));
    });

    test('Theme variants should be properly configured', () {
      final standard = ThemeVariantConfig.standard;
      final premium = ThemeVariantConfig.premium;
      final accessibility = ThemeVariantConfig.accessibility;

      expect(standard.variant, ThemeVariant.standard);
      expect(premium.variant, ThemeVariant.premium);
      expect(accessibility.variant, ThemeVariant.accessibility);

      // Accessibility variant should have animations disabled
      expect(accessibility.settings.enableAnimations, false);
      expect(accessibility.settings.defaultAnimationDuration, Duration.zero);
    });

    test('Responsive breakpoints should be properly defined', () {
      const breakpoints = ThemeConfig.breakpoints;

      expect(breakpoints.mobile, 480);
      expect(breakpoints.tablet, 768);
      expect(breakpoints.desktop, 1024);
      expect(breakpoints.largeDesktop, 1440);
    });
  });

  group('Responsive Design Tests', () {
    testWidgets('Device type detection works correctly', (tester) async {
      await tester.binding.setSurfaceSize(const Size(400, 600)); // Mobile
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              expect(Responsive.getDeviceType(context), DeviceType.mobile);
              expect(Responsive.isMobile(context), true);
              expect(Responsive.isTablet(context), false);
              return Container();
            },
          ),
        ),
      );

      await tester.binding.setSurfaceSize(const Size(800, 600)); // Tablet
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              expect(Responsive.getDeviceType(context), DeviceType.tablet);
              expect(Responsive.isTablet(context), true);
              expect(Responsive.isMobile(context), false);
              return Container();
            },
          ),
        ),
      );

      await tester.binding.setSurfaceSize(null); // Reset
    });

    testWidgets('Responsive values work correctly', (tester) async {
      await tester.binding.setSurfaceSize(const Size(400, 600)); // Mobile
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              final value = Responsive.value(
                context,
                mobile: 16.0,
                tablet: 24.0,
                desktop: 32.0,
              );
              expect(value, 16.0);
              return Container();
            },
          ),
        ),
      );

      await tester.binding.setSurfaceSize(const Size(800, 600)); // Tablet
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              final value = Responsive.value(
                context,
                mobile: 16.0,
                tablet: 24.0,
                desktop: 32.0,
              );
              expect(value, 24.0);
              return Container();
            },
          ),
        ),
      );

      await tester.binding.setSurfaceSize(null); // Reset
    });
  });

  group('Theme Service Tests', () {
    test('Mock theme service works correctly', () async {
      final service = ThemeTestUtils.createMockThemeService();

      // Test initial state
      expect(await service.getThemeMode(), AppThemeMode.system);

      // Test setting theme mode
      await service.setThemeMode(AppThemeMode.dark);
      expect(await service.getThemeMode(), AppThemeMode.dark);

      // Test getting settings
      final settings = await service.getThemeSettings();
      expect(settings.useMaterial3, true);

      // Test export/import
      final config = await service.exportThemeConfig();
      expect(config['version'], '1.0.0');
      expect(config['themeMode'], 'dark');
    });

    test('Theme service handles errors correctly', () async {
      final service = ThemeTestUtils.createMockThemeService(shouldThrowErrors: true);

      expect(() => service.getThemeMode(), throwsException);
      expect(() => service.setThemeMode(AppThemeMode.dark), throwsException);
      expect(() => service.getThemeSettings(), throwsException);
    });
  });

  group('Themed Components Tests', () {
    testWidgets('ThemedButton renders correctly', (tester) async {
      await tester.pumpWithTheme(
        const ThemedButton(
          text: 'Test Button',
          type: ButtonType.primary,
        ),
      );

      expect(find.text('Test Button'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('ThemedCard renders correctly', (tester) async {
      await tester.pumpWithTheme(
        const ThemedCard(
          child: Text('Card Content'),
        ),
      );

      expect(find.text('Card Content'), findsOneWidget);
      expect(find.byType(Card), findsOneWidget);
    });

    testWidgets('ThemedTextField renders correctly', (tester) async {
      await tester.pumpWithTheme(
        const ThemedTextField(
          label: 'Test Field',
          hint: 'Enter text',
        ),
      );

      expect(find.byType(TextFormField), findsOneWidget);
    });
  });
}
